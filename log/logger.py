"""
日志配置模块
复用现有项目的日志模式，提供统一的日志管理
"""

import logging
import sys
from typing import Optional
from pathlib import Path

from config.settings import settings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


def setup_logger(
    name: str,
    level: Optional[str] = None,
    format_string: Optional[str] = None,
    use_color: bool = True
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        format_string: 日志格式字符串
        use_color: 是否使用彩色输出
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = level or settings.LOG_LEVEL
    logger.setLevel(getattr(logging, log_level))
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level))
    
    # 设置格式化器
    format_str = format_string or settings.LOG_FORMAT
    if use_color and sys.stdout.isatty():
        formatter = ColoredFormatter(format_str)
    else:
        formatter = logging.Formatter(format_str)
    
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 防止日志向上传播
    logger.propagate = False
    
    return logger


def setup_file_logger(
    name: str,
    log_file: str,
    level: Optional[str] = None,
    format_string: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    设置文件日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别
        format_string: 日志格式字符串
        max_bytes: 单个日志文件最大字节数
        backup_count: 备份文件数量
        
    Returns:
        配置好的日志记录器
    """
    from logging.handlers import RotatingFileHandler
    
    logger = logging.getLogger(name)
    
    # 设置日志级别
    log_level = level or settings.LOG_LEVEL
    logger.setLevel(getattr(logging, log_level))
    
    # 确保日志目录存在
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建文件处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level))
    
    # 设置格式化器
    format_str = format_string or settings.LOG_FORMAT
    formatter = logging.Formatter(format_str)
    file_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    
    return logger


# 预定义的日志记录器
def get_main_logger() -> logging.Logger:
    """获取主应用日志记录器"""
    return setup_logger("llm_proxy.main")


def get_config_logger() -> logging.Logger:
    """获取配置模块日志记录器"""
    return setup_logger("llm_proxy.config")


def get_format_detector_logger() -> logging.Logger:
    """获取格式检测服务日志记录器"""
    return setup_logger("llm_proxy.format_detector")


def get_litellm_converter_logger() -> logging.Logger:
    """获取LiteLLM转换服务日志记录器"""
    return setup_logger("llm_proxy.litellm_converter")


def get_proxy_forwarder_logger() -> logging.Logger:
    """获取代理转发服务日志记录器"""
    return setup_logger("llm_proxy.proxy_forwarder")


def get_router_logger() -> logging.Logger:
    """获取路由模块日志记录器"""
    return setup_logger("llm_proxy.router")


def get_middleware_logger() -> logging.Logger:
    """获取中间件日志记录器"""
    return setup_logger("llm_proxy.middleware")


def get_application_logger() -> logging.Logger:
    """获取应用程序日志记录器"""
    return setup_logger("llm_proxy.application")


# 设置根日志记录器
def setup_root_logger():
    """设置根日志记录器"""
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # 如果没有处理器，添加一个默认的
    if not root_logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(settings.LOG_FORMAT)
        handler.setFormatter(formatter)
        root_logger.addHandler(handler)


# 初始化时设置根日志记录器
setup_root_logger()
