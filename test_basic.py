#!/usr/bin/env python3
"""
基础功能测试脚本
用于验证项目基础架构是否正常工作
"""

import asyncio
import sys
from fastapi.testclient import TestClient

def test_configuration():
    """测试配置系统"""
    print("Testing configuration system...")
    try:
        from config.settings import settings
        print(f"✓ Proxy server: {settings.PROXY_HOST}:{settings.PROXY_PORT}")
        print(f"✓ Target server: {settings.TARGET_BASE_URL}")
        print(f"✓ Log level: {settings.LOG_LEVEL}")
        print("✓ Configuration system working!")
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """测试日志系统"""
    print("\nTesting logging system...")
    try:
        from log.logger import get_main_logger, get_application_logger
        
        main_logger = get_main_logger()
        app_logger = get_application_logger()
        
        main_logger.info("Main logger test")
        app_logger.info("Application logger test")
        
        print("✓ Logging system working!")
        return True
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_fastapi_app():
    """测试 FastAPI 应用"""
    print("\nTesting FastAPI application...")
    try:
        from core.application import create_app
        
        app = create_app()
        print(f"✓ App created: {app.title} v{app.version}")
        
        # 测试健康检查端点
        with TestClient(app) as client:
            response = client.get("/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Health check endpoint working: {data['status']}")
            else:
                print(f"✗ Health check failed: {response.status_code}")
                return False
            
            # 测试服务信息端点
            response = client.get("/info")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Info endpoint working: {data['service']}")
            else:
                print(f"✗ Info endpoint failed: {response.status_code}")
                return False
        
        print("✓ FastAPI application working!")
        return True
    except Exception as e:
        print(f"✗ FastAPI test failed: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    print("\nTesting project structure...")
    try:
        import os
        
        required_dirs = [
            "config", "core", "service", "router", "models", 
            "handler", "middleware", "log", "utils", "tests"
        ]
        
        for dir_name in required_dirs:
            if os.path.exists(dir_name) and os.path.isdir(dir_name):
                print(f"✓ Directory exists: {dir_name}")
            else:
                print(f"✗ Missing directory: {dir_name}")
                return False
        
        required_files = [
            "main.py", "requirements.txt", "README.md", 
            ".env.example", "__init__.py"
        ]
        
        for file_name in required_files:
            if os.path.exists(file_name) and os.path.isfile(file_name):
                print(f"✓ File exists: {file_name}")
            else:
                print(f"✗ Missing file: {file_name}")
                return False
        
        print("✓ Project structure complete!")
        return True
    except Exception as e:
        print(f"✗ Project structure test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("LLM Proxy Server - Basic Infrastructure Test")
    print("=" * 50)
    
    tests = [
        test_project_structure,
        test_configuration,
        test_logging,
        test_fastapi_app,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"\n✗ Test failed: {test.__name__}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Basic infrastructure is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
