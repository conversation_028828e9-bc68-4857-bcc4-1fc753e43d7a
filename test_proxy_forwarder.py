#!/usr/bin/env python3
"""
代理转发服务测试脚本
用于验证 HTTP 代理转发功能是否正常工作
"""

import asyncio
import json
import sys
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock, patch
import httpx

def test_imports():
    """测试导入"""
    print("Testing imports...")
    try:
        from service.client.proxy_client import ProxyClient, BaseProxyClient
        from service.proxy_forwarder import ProxyForwarder
        from handler.proxy_retry_handler import ProxyRetryHandler, SmartRetryHandler
        print("✓ Proxy forwarder imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_proxy_client_initialization():
    """测试代理客户端初始化"""
    print("\nTesting proxy client initialization...")
    try:
        from service.client.proxy_client import ProxyClient
        
        # 测试基础初始化
        client = ProxyClient("http://localhost:8001")
        print("✓ Proxy client initialized")
        
        # 测试统计信息
        stats = client.get_stats()
        print(f"✓ Client stats: {stats['base_url']}")
        
        return True
    except Exception as e:
        print(f"✗ Proxy client initialization failed: {e}")
        return False

def test_proxy_forwarder_initialization():
    """测试代理转发服务初始化"""
    print("\nTesting proxy forwarder initialization...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        # 测试基础初始化
        forwarder = ProxyForwarder("http://localhost:8001")
        print("✓ Proxy forwarder initialized")
        
        # 测试统计信息
        stats = forwarder.get_stats()
        print(f"✓ Forwarder stats: {stats['target_base_url']}")
        
        return True
    except Exception as e:
        print(f"✗ Proxy forwarder initialization failed: {e}")
        return False

async def test_mock_request_forwarding():
    """测试模拟请求转发"""
    print("\nTesting mock request forwarding...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        # 创建转发器
        forwarder = ProxyForwarder("http://localhost:8001")
        
        # 模拟成功的 HTTP 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'{"message": "success"}'
        mock_response.headers = {"content-type": "application/json"}
        
        # 模拟客户端
        with patch.object(forwarder.client, 'forward_request', return_value=mock_response):
            response = await forwarder.forward_request(
                method="POST",
                path="/v1/chat/completions",
                headers={"authorization": "Bearer test-key"},
                data={"model": "gpt-3.5-turbo", "messages": []}
            )
            
            if response.status_code == 200:
                print("✓ Mock request forwarding successful")
                return True
            else:
                print(f"✗ Unexpected status code: {response.status_code}")
                return False
        
    except Exception as e:
        print(f"✗ Mock request forwarding failed: {e}")
        return False

async def test_stream_request_simulation():
    """测试流式请求模拟"""
    print("\nTesting stream request simulation...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        # 创建转发器
        forwarder = ProxyForwarder("http://localhost:8001")
        
        # 模拟流式响应生成器
        async def mock_stream_generator():
            chunks = [
                b'data: {"choices": [{"delta": {"content": "Hello"}}]}\n\n',
                b'data: {"choices": [{"delta": {"content": " world"}}]}\n\n',
                b'data: [DONE]\n\n'
            ]
            for chunk in chunks:
                yield chunk
        
        # 模拟客户端流式请求
        with patch.object(forwarder.client, 'forward_stream_request', return_value=mock_stream_generator()):
            response = await forwarder.forward_request(
                method="POST",
                path="/v1/chat/completions",
                headers={"authorization": "Bearer test-key"},
                data={"model": "gpt-3.5-turbo", "messages": [], "stream": True},
                stream=True
            )
            
            if hasattr(response, 'body_iterator'):
                print("✓ Stream request simulation successful")
                return True
            else:
                print("✓ Stream response created (StreamingResponse)")
                return True
        
    except Exception as e:
        print(f"✗ Stream request simulation failed: {e}")
        return False

def test_retry_handler():
    """测试重试处理器"""
    print("\nTesting retry handler...")
    try:
        from handler.proxy_retry_handler import ProxyRetryHandler, SmartRetryHandler
        
        # 测试基础重试处理器
        retry_handler = ProxyRetryHandler(max_retries=3, retry_delay=0.1)
        print("✓ Basic retry handler created")
        
        # 测试智能重试处理器
        smart_handler = SmartRetryHandler(max_retries=3, base_delay=0.1)
        print("✓ Smart retry handler created")
        
        # 测试错误分类
        from handler.proxy_retry_handler import ProxyErrorHandler
        error_handler = ProxyErrorHandler()
        
        # 模拟可重试错误
        timeout_error = httpx.TimeoutException("Request timeout")
        is_retryable = error_handler.is_retryable_error(timeout_error)
        
        if is_retryable:
            print("✓ Timeout error correctly identified as retryable")
        else:
            print("✗ Timeout error should be retryable")
            return False
        
        # 测试重试延迟计算
        delay = error_handler.get_retry_delay(2, 1.0)  # 第3次尝试
        expected_delay = 1.0 * (2 ** 2)  # 4.0 秒
        
        if delay == expected_delay:
            print(f"✓ Retry delay calculation correct: {delay}s")
        else:
            print(f"✗ Retry delay calculation incorrect: expected {expected_delay}, got {delay}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Retry handler test failed: {e}")
        return False

async def test_retry_decorator():
    """测试重试装饰器"""
    print("\nTesting retry decorator...")
    try:
        from handler.proxy_retry_handler import ProxyRetryHandler
        
        # 创建重试装饰器
        retry = ProxyRetryHandler(max_retries=3, retry_delay=0.01)  # 快速测试
        
        # 测试成功情况
        @retry
        async def successful_function():
            return "success"
        
        result = await successful_function()
        if result == "success":
            print("✓ Successful function with retry decorator")
        else:
            print("✗ Unexpected result from successful function")
            return False
        
        # 测试失败情况
        attempt_count = 0
        
        @retry
        async def failing_function():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise Exception(f"Attempt {attempt_count} failed")
            return "success after retries"
        
        result = await failing_function()
        if result == "success after retries" and attempt_count == 3:
            print(f"✓ Function succeeded after {attempt_count} attempts")
        else:
            print(f"✗ Unexpected retry behavior: result={result}, attempts={attempt_count}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Retry decorator test failed: {e}")
        return False

async def test_health_check():
    """测试健康检查"""
    print("\nTesting health check...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("http://localhost:8001")
        
        # 模拟健康检查响应
        with patch.object(forwarder.client, 'health_check', return_value=True):
            health_status = await forwarder.health_check()
            
            if health_status["status"] == "healthy":
                print("✓ Health check successful")
                print(f"✓ Target server: {health_status['target_server']}")
                return True
            else:
                print(f"✗ Unexpected health status: {health_status['status']}")
                return False
        
    except Exception as e:
        print(f"✗ Health check test failed: {e}")
        return False

def test_api_key_extraction():
    """测试 API 密钥提取"""
    print("\nTesting API key extraction...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("http://localhost:8001")
        
        # 测试 Bearer token
        headers1 = {"authorization": "Bearer sk-test123"}
        api_key1 = forwarder._extract_api_key(headers1)
        
        if api_key1 == "sk-test123":
            print("✓ Bearer token extraction successful")
        else:
            print(f"✗ Bearer token extraction failed: {api_key1}")
            return False
        
        # 测试直接 API 密钥
        headers2 = {"x-api-key": "direct-key-456"}
        api_key2 = forwarder._extract_api_key(headers2)
        
        if api_key2 == "direct-key-456":
            print("✓ Direct API key extraction successful")
        else:
            print(f"✗ Direct API key extraction failed: {api_key2}")
            return False
        
        # 测试无 API 密钥
        headers3 = {"content-type": "application/json"}
        api_key3 = forwarder._extract_api_key(headers3)
        
        if api_key3 is None:
            print("✓ No API key extraction successful")
        else:
            print(f"✗ Should return None for no API key: {api_key3}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ API key extraction test failed: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - Proxy Forwarder Test")
    print("=" * 70)
    
    tests = [
        ("Import Test", test_imports),
        ("Proxy Client Initialization", test_proxy_client_initialization),
        ("Proxy Forwarder Initialization", test_proxy_forwarder_initialization),
        ("Mock Request Forwarding", test_mock_request_forwarding),
        ("Stream Request Simulation", test_stream_request_simulation),
        ("Retry Handler", test_retry_handler),
        ("Retry Decorator", test_retry_decorator),
        ("Health Check", test_health_check),
        ("API Key Extraction", test_api_key_extraction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All proxy forwarder tests passed! Service is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
