#!/usr/bin/env python3
"""
测试不同URL的访问
"""

import requests
import json

def test_url(base_url, name):
    """测试指定URL"""
    try:
        url = f"{base_url}/health"
        print(f"测试 {name}: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"✓ {name} 可以访问")
            return True
        else:
            print(f"✗ {name} 返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ {name} 访问失败: {e}")
        return False

def test_chat_url(base_url, name):
    """测试聊天接口"""
    try:
        url = f"{base_url}/v1/chat/completions"
        headers = {
            "Authorization": "Bearer 3",
            "Content-Type": "application/json"
        }
        data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "stream": False
        }
        
        print(f"测试聊天接口 {name}: {url}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✓ {name} 聊天接口正常")
            return True
        else:
            print(f"✗ {name} 聊天接口失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ {name} 聊天接口异常: {e}")
        return False

def main():
    print("=" * 60)
    print("URL 访问测试")
    print("=" * 60)
    
    urls_to_test = [
        ("http://localhost:8002", "localhost"),
        ("http://127.0.0.1:8002", "127.0.0.1"),
        ("http://0.0.0.0:8002", "0.0.0.0"),
    ]
    
    results = {}
    
    for base_url, name in urls_to_test:
        print(f"\n--- 测试 {name} ---")
        
        # 测试健康检查
        health_ok = test_url(base_url, f"{name} 健康检查")
        
        # 测试聊天接口
        chat_ok = test_chat_url(base_url, f"{name} 聊天接口")
        
        results[name] = {"health": health_ok, "chat": chat_ok}
        
        print()
    
    print("=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    for name, result in results.items():
        health_status = "✓" if result["health"] else "✗"
        chat_status = "✓" if result["chat"] else "✗"
        print(f"{name:12} | 健康检查: {health_status} | 聊天接口: {chat_status}")
    
    print("\n推荐配置:")
    for name, result in results.items():
        if result["health"] and result["chat"]:
            print(f"✓ 推荐使用: http://{name}:8002")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
