#!/usr/bin/env python3
"""
测试用户的确切配置
"""

import requests
import json

def test_gemini_agent_config():
    """测试Gemini 2.5 Flash (Agent)的确切配置"""
    
    # 用户的确切配置
    config = {
        "name": "Gemini 2.5 Flash (Agent)",
        "provider": "gemini", 
        "model": "gemini-2.5-flash",
        "apiBase": "http://127.0.0.1:8002",
        "apiKey": "3"
    }
    
    print("=" * 60)
    print("测试用户的确切配置")
    print("=" * 60)
    print(f"配置信息:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print("=" * 60)
    
    # 1. 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        health_url = f"{config['apiBase']}/health"
        print(f"URL: {health_url}")
        
        response = requests.get(health_url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ 健康检查成功")
            print(f"响应: {response.json()}")
        else:
            print(f"✗ 健康检查失败: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False
    
    # 2. 测试OpenAI格式的聊天接口
    print("\n2. 测试OpenAI格式聊天接口...")
    try:
        chat_url = f"{config['apiBase']}/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {config['apiKey']}",
            "Content-Type": "application/json"
        }
        data = {
            "model": config["model"],
            "messages": [
                {"role": "user", "content": "Hello, this is a test from the exact user config"}
            ],
            "stream": False
        }
        
        print(f"URL: {chat_url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(chat_url, headers=headers, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ OpenAI格式聊天成功")
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"✗ OpenAI格式聊天失败")
            print(f"错误响应: {response.text}")
            return False
    except Exception as e:
        print(f"✗ OpenAI格式聊天异常: {e}")
        return False
    
    # 3. 测试Gemini原生格式
    print("\n3. 测试Gemini原生格式...")
    try:
        gemini_url = f"{config['apiBase']}/models/{config['model']}:generateContent"
        headers = {
            "Content-Type": "application/json"
        }
        params = {"key": config['apiKey']}
        data = {
            "contents": [
                {
                    "parts": [
                        {"text": "Hello from Gemini native format test"}
                    ]
                }
            ]
        }
        
        print(f"URL: {gemini_url}")
        print(f"Params: {params}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(gemini_url, headers=headers, json=data, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ Gemini原生格式成功")
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"✗ Gemini原生格式失败")
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"✗ Gemini原生格式异常: {e}")
    
    # 4. 测试流式请求
    print("\n4. 测试流式请求...")
    try:
        stream_url = f"{config['apiBase']}/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {config['apiKey']}",
            "Content-Type": "application/json"
        }
        data = {
            "model": config["model"],
            "messages": [
                {"role": "user", "content": "Hello, test streaming"}
            ],
            "stream": True
        }
        
        print(f"URL: {stream_url}")
        print("测试流式响应...")
        
        response = requests.post(stream_url, headers=headers, json=data, timeout=30, stream=True)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ 流式请求成功")
            # 读取前几行流式响应
            lines_read = 0
            for line in response.iter_lines():
                if line and lines_read < 3:
                    print(f"流式数据: {line.decode()}")
                    lines_read += 1
                elif lines_read >= 3:
                    break
        else:
            print(f"✗ 流式请求失败: {response.text}")
    except Exception as e:
        print(f"✗ 流式请求异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_gemini_agent_config()
