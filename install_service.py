#!/usr/bin/env python3
"""
Windows 服务安装脚本
用于安装、卸载和管理 LLM 代理服务器 Windows 服务
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent.absolute()

def check_admin_privileges():
    """检查是否具有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def install_pywin32():
    """安装 pywin32 依赖"""
    print("正在安装 pywin32...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pywin32"], 
                      check=True, cwd=project_root)
        print("pywin32 安装成功")
        
        # 运行 pywin32 后安装脚本
        print("正在配置 pywin32...")
        subprocess.run([sys.executable, "-m", "pywin32_postinstall", "-install"], 
                      check=True, cwd=project_root)
        print("pywin32 配置完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装 pywin32 失败: {e}")
        return False

def install_service():
    """安装服务"""
    print("正在安装 LLM 代理服务...")
    
    try:
        # 安装服务
        cmd = [sys.executable, "service_wrapper.py", "install"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 服务安装成功")
            
            # 设置服务为自动启动
            print("正在设置服务为自动启动...")
            cmd = [sys.executable, "service_wrapper.py", "update", "--startup=auto"]
            result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ 服务已设置为自动启动")
            else:
                print(f"⚠ 设置自动启动失败: {result.stderr}")
                
            return True
        else:
            print(f"✗ 服务安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 安装服务时出错: {e}")
        return False

def uninstall_service():
    """卸载服务"""
    print("正在卸载 LLM 代理服务...")
    
    try:
        # 先停止服务
        stop_service()
        time.sleep(2)
        
        # 卸载服务
        cmd = [sys.executable, "service_wrapper.py", "remove"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 服务卸载成功")
            return True
        else:
            print(f"✗ 服务卸载失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 卸载服务时出错: {e}")
        return False

def start_service():
    """启动服务"""
    print("正在启动 LLM 代理服务...")
    
    try:
        cmd = [sys.executable, "service_wrapper.py", "start"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 服务启动成功")
            
            # 等待几秒钟让服务完全启动
            print("等待服务启动...")
            time.sleep(5)
            
            # 检查服务状态
            check_service_status()
            return True
        else:
            print(f"✗ 服务启动失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 启动服务时出错: {e}")
        return False

def stop_service():
    """停止服务"""
    print("正在停止 LLM 代理服务...")
    
    try:
        cmd = [sys.executable, "service_wrapper.py", "stop"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 服务停止成功")
            return True
        else:
            print(f"⚠ 服务停止失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 停止服务时出错: {e}")
        return False

def check_service_status():
    """检查服务状态"""
    try:
        # 检查服务状态
        cmd = ["sc", "query", "LLMProxyServer"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            if "RUNNING" in output:
                print("✓ 服务正在运行")
                
                # 测试 API 接口
                print("正在测试 API 接口...")
                test_api()
            elif "STOPPED" in output:
                print("⚠ 服务已停止")
            else:
                print(f"服务状态: {output}")
        else:
            print("✗ 服务未安装或查询失败")
            
    except Exception as e:
        print(f"✗ 检查服务状态时出错: {e}")

def test_api():
    """测试 API 接口"""
    try:
        import time
        time.sleep(2)  # 等待服务完全启动
        
        cmd = ["powershell", "-Command", 
               "Invoke-WebRequest -Uri http://localhost:8002/health -Method GET -TimeoutSec 10"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and "200" in result.stdout:
            print("✓ API 接口测试成功")
        else:
            print("⚠ API 接口测试失败")
            print(f"响应: {result.stdout}")
            
    except Exception as e:
        print(f"⚠ API 测试时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("LLM 代理服务器 Windows 服务管理工具")
    print("=" * 60)
    
    # 检查管理员权限
    if not check_admin_privileges():
        print("✗ 错误: 需要管理员权限来管理 Windows 服务")
        print("请以管理员身份运行此脚本")
        input("按回车键退出...")
        sys.exit(1)
    
    print("✓ 管理员权限检查通过")
    
    # 检查并安装 pywin32
    try:
        import win32serviceutil
        print("✓ pywin32 已安装")
    except ImportError:
        print("pywin32 未安装，正在安装...")
        if not install_pywin32():
            print("✗ pywin32 安装失败，无法继续")
            input("按回车键退出...")
            sys.exit(1)
    
    while True:
        print("\n" + "=" * 40)
        print("请选择操作:")
        print("1. 安装服务")
        print("2. 卸载服务")
        print("3. 启动服务")
        print("4. 停止服务")
        print("5. 检查服务状态")
        print("6. 重启服务")
        print("0. 退出")
        print("=" * 40)
        
        choice = input("请输入选择 (0-6): ").strip()
        
        if choice == "1":
            install_service()
        elif choice == "2":
            uninstall_service()
        elif choice == "3":
            start_service()
        elif choice == "4":
            stop_service()
        elif choice == "5":
            check_service_status()
        elif choice == "6":
            stop_service()
            time.sleep(3)
            start_service()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
