#!/usr/bin/env python3
"""
测试格式转换功能
"""

import requests
import json

def test_openai_format():
    """测试OpenAI格式（不需要转换）"""
    try:
        url = "http://localhost:8002/v1/chat/completions"
        headers = {
            "Authorization": "Bearer 3",
            "Content-Type": "application/json"
        }
        data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {"role": "user", "content": "Hello from OpenAI format"}
            ],
            "stream": False
        }
        
        print("测试OpenAI格式...")
        print(f"请求: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ OpenAI格式测试成功")
            return True
        else:
            print(f"✗ OpenAI格式测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ OpenAI格式测试异常: {e}")
        return False

def test_gemini_format():
    """测试Gemini格式（需要转换）"""
    try:
        url = "http://localhost:8002/models/gemini-2.5-flash:generateContent"
        headers = {
            "Content-Type": "application/json"
        }
        params = {"key": "3"}
        data = {
            "contents": [
                {
                    "parts": [
                        {"text": "Hello from Gemini format"}
                    ]
                }
            ]
        }
        
        print("测试Gemini格式...")
        print(f"请求: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Gemini格式测试成功")
            return True
        else:
            print(f"✗ Gemini格式测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Gemini格式测试异常: {e}")
        return False

def main():
    print("=" * 60)
    print("LLM 格式转换测试")
    print("=" * 60)
    
    print("\n1. 测试OpenAI格式（无需转换）...")
    openai_ok = test_openai_format()
    
    print("\n" + "-" * 60)
    print("\n2. 测试Gemini格式（需要转换）...")
    gemini_ok = test_gemini_format()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"OpenAI格式: {'✓ 通过' if openai_ok else '✗ 失败'}")
    print(f"Gemini格式: {'✓ 通过' if gemini_ok else '✗ 失败'}")
    
    if openai_ok or gemini_ok:
        print("\n格式转换功能正常工作！")
    else:
        print("\n格式转换功能需要修复。")
    print("=" * 60)

if __name__ == "__main__":
    main()
