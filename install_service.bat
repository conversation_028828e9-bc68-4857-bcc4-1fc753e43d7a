@echo off
chcp 65001 >nul
echo ============================================================
echo LLM 代理服务器 Windows 服务安装脚本
echo ============================================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 管理员权限检查通过
) else (
    echo ✗ 错误: 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 切换到脚本所在目录
cd /d "%~dp0"

echo.
echo 正在安装 Windows 服务...
echo.

:: 运行 Python 安装脚本
py install_service.py

echo.
echo 安装完成！
pause
