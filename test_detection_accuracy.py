#!/usr/bin/env python3
"""
格式检测准确性测试脚本
测试各种边缘情况和准确性指标
"""

import asyncio
import sys
import time
from typing import List, Dict, Any
from unittest.mock import Mock

def create_mock_request(method: str, path: str, headers: Dict[str, str] = None, body: Dict[str, Any] = None):
    """创建模拟请求对象"""
    import json
    
    mock_request = Mock()
    mock_request.method = method
    
    mock_url = Mock()
    mock_url.path = path
    mock_request.url = mock_url
    
    mock_request.headers = headers or {}
    mock_request.query_params = {}
    
    if body:
        body_bytes = json.dumps(body).encode()
        mock_request._body = body_bytes
        
        async def mock_body():
            return body_bytes
        mock_request.body = mock_body
    else:
        async def mock_empty_body():
            return b""
        mock_request.body = mock_empty_body
    
    return mock_request

async def test_detection_accuracy():
    """测试检测准确性"""
    print("Testing detection accuracy...")
    
    from service.format_detector import FormatDetector
    from models.conversion_models import LLMFormat
    
    detector = FormatDetector()
    
    # 测试用例：(请求, 期望格式, 描述)
    test_cases = [
        # Gemini 测试用例
        (create_mock_request("POST", "/v1beta/models/gemini-pro:generateContent", 
                            {"x-goog-api-key": "test"}, 
                            {"contents": [{"role": "user", "parts": [{"text": "hello"}]}]}), 
         LLMFormat.GEMINI, "Gemini generateContent with API key"),
        
        (create_mock_request("POST", "/gemini/v1beta/models/gemini-pro:streamGenerateContent", 
                            {}, 
                            {"contents": [{"role": "user", "parts": [{"text": "hello"}]}]}), 
         LLMFormat.GEMINI, "Gemini stream with prefix"),
        
        (create_mock_request("GET", "/v1beta/models"), 
         LLMFormat.GEMINI, "Gemini models list"),
        
        # OpenAI 测试用例
        (create_mock_request("POST", "/v1/chat/completions", 
                            {"authorization": "Bearer sk-test123"}, 
                            {"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "hello"}]}), 
         LLMFormat.OPENAI, "OpenAI chat completions"),
        
        (create_mock_request("POST", "/openai/v1/embeddings", 
                            {"authorization": "Bearer sk-test123"}, 
                            {"model": "text-embedding-ada-002", "input": "hello"}), 
         LLMFormat.OPENAI, "OpenAI embeddings with prefix"),
        
        (create_mock_request("GET", "/v1/models"), 
         LLMFormat.OPENAI, "OpenAI models list"),
        
        # Claude 测试用例
        (create_mock_request("POST", "/v1/messages", 
                            {"x-api-key": "test", "anthropic-version": "2023-06-01"}, 
                            {"model": "claude-3-sonnet", "messages": [{"role": "user", "content": "hello"}], "system": "assistant"}), 
         LLMFormat.CLAUDE, "Claude messages with system"),
        
        (create_mock_request("POST", "/claude/v1/messages", 
                            {"x-api-key": "test"}, 
                            {"model": "claude-3-sonnet", "messages": [{"role": "user", "content": "hello"}]}), 
         LLMFormat.CLAUDE, "Claude with prefix"),
        
        # Cohere 测试用例
        (create_mock_request("POST", "/v1/generate", 
                            {}, 
                            {"model": "command", "message": "hello", "chat_history": []}), 
         LLMFormat.COHERE, "Cohere generate with chat history"),
        
        (create_mock_request("POST", "/cohere/v1/chat", 
                            {}, 
                            {"model": "command", "message": "hello"}), 
         LLMFormat.COHERE, "Cohere chat with prefix"),
        
        # HuggingFace 测试用例
        (create_mock_request("POST", "/hf/v1/chat/completions", 
                            {"authorization": "Bearer hf_test123"}, 
                            {"inputs": "hello", "parameters": {"temperature": 0.7}}), 
         LLMFormat.HUGGINGFACE, "HuggingFace with inputs and parameters"),
        
        # Ollama 测试用例
        (create_mock_request("POST", "/ollama/api/generate", 
                            {}, 
                            {"model": "llama2", "prompt": "hello", "options": {}}), 
         LLMFormat.OLLAMA, "Ollama generate with prompt"),
        
        (create_mock_request("POST", "/api/chat", 
                            {}, 
                            {"model": "llama2", "messages": [{"role": "user", "content": "hello"}]}), 
         LLMFormat.OLLAMA, "Ollama chat with messages"),
        
        # 边缘情况
        (create_mock_request("POST", "/unknown/endpoint", 
                            {}, 
                            {"unknown": "data"}), 
         LLMFormat.UNKNOWN, "Unknown format"),
        
        (create_mock_request("GET", "/health"), 
         LLMFormat.UNKNOWN, "Health check endpoint"),
    ]
    
    correct_predictions = 0
    total_predictions = len(test_cases)
    
    print(f"Running {total_predictions} test cases...")
    
    for i, (request, expected_format, description) in enumerate(test_cases, 1):
        try:
            result = await detector.detect_format(request)
            predicted_format = result.detected_format
            confidence = result.confidence
            
            is_correct = predicted_format == expected_format
            if is_correct:
                correct_predictions += 1
                status = "✓"
            else:
                status = "✗"
            
            print(f"{status} Test {i:2d}: {description}")
            print(f"    Expected: {expected_format.value}, Got: {predicted_format.value}, Confidence: {confidence:.2f}")
            
        except Exception as e:
            print(f"✗ Test {i:2d}: {description} - ERROR: {e}")
    
    accuracy = correct_predictions / total_predictions
    print(f"\nAccuracy: {correct_predictions}/{total_predictions} = {accuracy:.2%}")
    
    # 验证准确率是否达到要求（95%）
    required_accuracy = 0.95
    if accuracy >= required_accuracy:
        print(f"✅ Accuracy requirement met: {accuracy:.2%} >= {required_accuracy:.2%}")
        return True
    else:
        print(f"❌ Accuracy requirement not met: {accuracy:.2%} < {required_accuracy:.2%}")
        return False

async def test_performance():
    """测试性能"""
    print("\nTesting performance...")
    
    from service.format_detector import FormatDetector, CachedFormatDetector
    
    # 创建测试请求
    test_request = create_mock_request(
        "POST", 
        "/v1/chat/completions",
        {"authorization": "Bearer sk-test123"},
        {"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "hello"}]}
    )
    
    # 测试基础检测器性能
    detector = FormatDetector()
    
    start_time = time.time()
    for _ in range(100):
        await detector.detect_format(test_request)
    basic_time = time.time() - start_time
    
    print(f"✓ Basic detector: 100 detections in {basic_time:.3f}s ({100/basic_time:.1f} req/s)")
    
    # 测试缓存检测器性能
    cached_detector = CachedFormatDetector()
    
    start_time = time.time()
    for _ in range(100):
        await cached_detector.detect_format(test_request)
    cached_time = time.time() - start_time
    
    cache_stats = cached_detector.get_cache_stats()
    print(f"✓ Cached detector: 100 detections in {cached_time:.3f}s ({100/cached_time:.1f} req/s)")
    print(f"✓ Cache hit rate: {cache_stats['hit_rate']:.2%}")
    
    # 验证性能要求（延迟 < 100ms）
    avg_latency = basic_time / 100 * 1000  # 转换为毫秒
    if avg_latency < 100:
        print(f"✅ Performance requirement met: {avg_latency:.1f}ms < 100ms")
        return True
    else:
        print(f"❌ Performance requirement not met: {avg_latency:.1f}ms >= 100ms")
        return False

async def test_confidence_scoring():
    """测试置信度评分"""
    print("\nTesting confidence scoring...")
    
    from service.format_detector import FormatDetector
    
    detector = FormatDetector()
    
    # 高置信度测试用例
    high_confidence_request = create_mock_request(
        "POST", 
        "/v1beta/models/gemini-pro:generateContent",
        {"x-goog-api-key": "test-key"},
        {"contents": [{"role": "user", "parts": [{"text": "hello"}]}], "generationConfig": {"temperature": 0.7}}
    )
    
    result = await detector.detect_format(high_confidence_request)
    high_confidence = result.confidence
    
    # 低置信度测试用例
    low_confidence_request = create_mock_request(
        "POST",
        "/some/ambiguous/path",
        {},
        {"some_field": "some_value"}
    )
    
    result = await detector.detect_format(low_confidence_request)
    low_confidence = result.confidence
    
    print(f"✓ High confidence case: {high_confidence:.2f}")
    print(f"✓ Low confidence case: {low_confidence:.2f}")
    
    # 验证置信度差异
    if high_confidence > low_confidence:
        print("✅ Confidence scoring working correctly")
        return True
    else:
        print("❌ Confidence scoring not working as expected")
        return False

async def test_edge_cases():
    """测试边缘情况"""
    print("\nTesting edge cases...")
    
    from service.format_detector import FormatDetector
    
    detector = FormatDetector()
    
    edge_cases = [
        ("Empty path", create_mock_request("POST", "")),
        ("Root path", create_mock_request("GET", "/")),
        ("No body", create_mock_request("POST", "/v1/chat/completions")),
        ("Invalid JSON body", Mock()),  # 特殊处理
        ("Very long path", create_mock_request("GET", "/" + "a" * 1000)),
    ]
    
    passed = 0
    for description, request in edge_cases:
        try:
            if description == "Invalid JSON body":
                # 特殊处理无效 JSON 的情况
                request = Mock()
                request.method = "POST"
                request.url = Mock()
                request.url.path = "/v1/chat/completions"
                request.headers = {}
                request.query_params = {}
                
                async def invalid_body():
                    return b"invalid json {"
                request.body = invalid_body
            
            result = await detector.detect_format(request)
            print(f"✓ {description}: {result.detected_format.value} (confidence: {result.confidence:.2f})")
            passed += 1
        except Exception as e:
            print(f"✗ {description}: Error - {e}")
    
    if passed == len(edge_cases):
        print("✅ All edge cases handled correctly")
        return True
    else:
        print(f"❌ {len(edge_cases) - passed} edge cases failed")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - Format Detection Accuracy Test")
    print("=" * 70)
    
    tests = [
        ("Detection Accuracy", test_detection_accuracy),
        ("Performance", test_performance),
        ("Confidence Scoring", test_confidence_scoring),
        ("Edge Cases", test_edge_cases),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All accuracy tests passed! Format detector meets all requirements.")
        return 0
    else:
        print("❌ Some tests failed. Please check the requirements.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
