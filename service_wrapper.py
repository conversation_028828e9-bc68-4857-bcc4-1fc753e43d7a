#!/usr/bin/env python3
"""
Windows 服务包装器
将 LLM 代理服务器包装为 Windows 服务
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
except ImportError:
    print("错误: 需要安装 pywin32 库")
    print("请运行: pip install pywin32")
    sys.exit(1)

# 导入项目模块
from dotenv import load_dotenv
load_dotenv()

from core.application import create_app
from log.logger import get_main_logger
import uvicorn


class LLMProxyService(win32serviceutil.ServiceFramework):
    """LLM 代理服务器 Windows 服务类"""
    
    # 服务配置
    _svc_name_ = "LLMProxyServer"
    _svc_display_name_ = "LLM Format Proxy Server"
    _svc_description_ = "基于 LiteLLM 的智能 LLM 格式转换代理服务器"
    
    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.logger = self._setup_service_logger()
        self.server = None
        
    def _setup_service_logger(self):
        """设置服务日志"""
        logger = logging.getLogger('LLMProxyService')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "service.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
        
    def SvcStop(self):
        """停止服务"""
        self.logger.info("正在停止 LLM 代理服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        
        # 停止 uvicorn 服务器
        if self.server:
            try:
                self.server.should_exit = True
                self.logger.info("Uvicorn 服务器停止信号已发送")
            except Exception as e:
                self.logger.error(f"停止服务器时出错: {e}")
                
    def SvcDoRun(self):
        """运行服务"""
        try:
            self.logger.info("正在启动 LLM 代理服务...")
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
            
            # 切换到项目目录
            os.chdir(project_root)
            
            # 导入配置
            from config.settings import settings
            
            self.logger.info(f"服务将监听 {settings.PROXY_HOST}:{settings.PROXY_PORT}")
            self.logger.info(f"目标服务器: {settings.TARGET_HOST}:{settings.TARGET_PORT}")
            
            # 创建 FastAPI 应用
            app = create_app()
            
            # 配置 uvicorn
            config = uvicorn.Config(
                app=app,
                host=settings.PROXY_HOST,
                port=settings.PROXY_PORT,
                log_level="info",
                access_log=True,
                loop="asyncio"
            )
            
            # 创建服务器实例
            self.server = uvicorn.Server(config)
            
            self.logger.info("LLM 代理服务启动成功")
            
            # 在单独的线程中运行服务器
            import threading
            import asyncio
            
            def run_server():
                """在新的事件循环中运行服务器"""
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    # 运行服务器
                    loop.run_until_complete(self.server.serve())
                except Exception as e:
                    self.logger.error(f"服务器运行错误: {e}")
                finally:
                    loop.close()
            
            # 启动服务器线程
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            
            # 等待停止信号
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            
            self.logger.info("LLM 代理服务已停止")
            
        except Exception as e:
            self.logger.error(f"服务运行时出错: {e}")
            servicemanager.LogErrorMsg(f"LLM 代理服务错误: {e}")
            raise


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(LLMProxyService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        win32serviceutil.HandleCommandLine(LLMProxyService)


if __name__ == '__main__':
    main()
