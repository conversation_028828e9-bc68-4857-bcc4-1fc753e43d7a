#!/usr/bin/env python3
"""
测试代理服务器API
"""

import requests
import json

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8002/health")
        print(f"健康检查状态码: {response.status_code}")
        print(f"健康检查响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_chat_completion():
    """测试聊天完成API"""
    try:
        url = "http://localhost:8002/v1/chat/completions"
        headers = {
            "Authorization": "Bearer 3",
            "Content-Type": "application/json"
        }
        data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {"role": "user", "content": "Hello, this is a test message"}
            ],
            "stream": False
        }
        
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"响应内容: {response.json()}")
        else:
            print(f"错误响应: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"聊天完成测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("LLM 代理服务器 API 测试")
    print("=" * 50)
    
    # 测试健康检查
    print("\n1. 测试健康检查...")
    health_ok = test_health()
    
    if not health_ok:
        print("健康检查失败，停止测试")
        return
    
    # 测试聊天完成
    print("\n2. 测试聊天完成API...")
    chat_ok = test_chat_completion()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"健康检查: {'✓ 通过' if health_ok else '✗ 失败'}")
    print(f"聊天完成: {'✓ 通过' if chat_ok else '✗ 失败'}")
    print("=" * 50)

if __name__ == "__main__":
    main()
