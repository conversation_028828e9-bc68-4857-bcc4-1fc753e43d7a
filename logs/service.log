2025-08-01 21:30:19,905 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:30:19,953 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:30:19,953 - LLMProxyService - INFO - 启动命令: py main.py
2025-08-01 21:30:19,956 - LLMProxyService - ERROR - 服务运行时出错: [WinError 2] 系统找不到指定的文件。
2025-08-01 21:32:13,427 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:32:13,428 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:32:13,429 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonservice.exe
2025-08-01 21:32:13,429 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonservice.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:32:13,431 - LLMProxyService - INFO - 服务进程已启动，PID: 28916
2025-08-01 21:32:13,432 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: C : \ U s e r s \ A d m i n i s t r a t o r \ A p p D a t a \ L o c a l \ P r o g r a m s \ P y t h o n \ P y t h o n 3 1 3 \ p y t h o n s e r v i c e . e x e   -   P y t h o n   S e r v i c e   M a n a g e r 
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP:  Options:
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: -debug servicename [parms] - debug the Python service.
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: 
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: NOTE: You do not start the service using this program - start the
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: service using Control Panel, or 'net start service_name'
2025-08-01 21:32:14,432 - LLMProxyService - ERROR - 服务进程意外结束，返回码: 2
2025-08-01 21:32:14,432 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:33:37,387 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:33:37,388 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:33:37,391 - LLMProxyService - INFO - 服务进程已启动，PID: 6288
2025-08-01 21:33:37,391 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:33:40,525 - LLMProxyService - INFO - APP: 2025-08-01 21:33:40,525 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:33:42,931 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:33:42,968 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [6288]
2025-08-01 21:33:42,968 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:33:42,969 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,969 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:33:42,969 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
2025-08-01 21:34:42,754 - LLMProxyService - INFO - APP: 2025-08-01 21:34:42,753 - llm_proxy.router - INFO - [1754055282753-3420] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:34:42,755 - LLMProxyService - INFO - APP: 2025-08-01 21:34:42,755 - llm_proxy.router - INFO - [1754055282753-3420] Detected format: gemini (confidence: 1.00)
2025-08-01 21:34:43,023 - LLMProxyService - INFO - APP: 2025-08-01 21:34:43,023 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:43,023 - LLMProxyService - INFO - APP: 2025-08-01 21:34:43,023 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:34:44,037 - LLMProxyService - INFO - APP: 2025-08-01 21:34:44,037 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:34:44,293 - LLMProxyService - INFO - APP: 2025-08-01 21:34:44,293 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:44,294 - LLMProxyService - INFO - APP: 2025-08-01 21:34:44,294 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:34:46,301 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,301 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:34:46,556 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,556 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:46,557 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,557 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:34:46,557 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,557 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:34:46,558 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,557 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:34:46,558 - LLMProxyService - INFO - APP: 2025-08-01 21:34:46,557 - llm_proxy.router - INFO - [1754055282753-3420] Request processed in 3804.0ms
2025-08-01 21:34:46,558 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:54195 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 500 Internal Server Error
2025-08-01 21:34:49,710 - LLMProxyService - INFO - APP: 2025-08-01 21:34:49,710 - llm_proxy.router - INFO - [1754055289710-3420] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:34:49,710 - LLMProxyService - INFO - APP: 2025-08-01 21:34:49,710 - llm_proxy.router - INFO - [1754055289710-3420] Detected format: gemini (confidence: 1.00)
2025-08-01 21:34:49,965 - LLMProxyService - INFO - APP: 2025-08-01 21:34:49,965 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:49,966 - LLMProxyService - INFO - APP: 2025-08-01 21:34:49,965 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:34:50,974 - LLMProxyService - INFO - APP: 2025-08-01 21:34:50,974 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:34:51,236 - LLMProxyService - INFO - APP: 2025-08-01 21:34:51,236 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:51,237 - LLMProxyService - INFO - APP: 2025-08-01 21:34:51,237 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:34:53,247 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,247 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:34:53,500 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,500 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:53,501 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,501 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:34:53,501 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,501 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:34:53,501 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,501 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:34:53,501 - LLMProxyService - INFO - APP: 2025-08-01 21:34:53,501 - llm_proxy.router - INFO - [1754055289710-3420] Request processed in 3791.3ms
2025-08-01 21:34:53,501 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:54401 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 500 Internal Server Error
2025-08-01 21:34:59,115 - LLMProxyService - INFO - APP: 2025-08-01 21:34:59,114 - llm_proxy.router - INFO - [1754055299114-3420] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:34:59,115 - LLMProxyService - INFO - APP: 2025-08-01 21:34:59,115 - llm_proxy.router - INFO - [1754055299114-3420] Detected format: gemini (confidence: 1.00)
2025-08-01 21:34:59,371 - LLMProxyService - INFO - APP: 2025-08-01 21:34:59,370 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:34:59,371 - LLMProxyService - INFO - APP: 2025-08-01 21:34:59,371 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:35:00,386 - LLMProxyService - INFO - APP: 2025-08-01 21:35:00,386 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:35:00,642 - LLMProxyService - INFO - APP: 2025-08-01 21:35:00,642 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:35:00,643 - LLMProxyService - INFO - APP: 2025-08-01 21:35:00,643 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:35:02,655 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,655 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:35:02,912 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,912 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:35:02,913 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,913 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:35:02,913 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,913 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:35:02,913 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,913 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:35:02,913 - LLMProxyService - INFO - APP: 2025-08-01 21:35:02,913 - llm_proxy.router - INFO - [1754055299114-3420] Request processed in 3798.2ms
2025-08-01 21:35:02,913 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:54676 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 500 Internal Server Error
2025-08-01 21:35:39,161 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:55844 - "GET /health HTTP/1.1" 200 OK
2025-08-01 21:36:17,361 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:56949 - "GET /health HTTP/1.1" 200 OK
2025-08-01 21:36:17,364 - LLMProxyService - INFO - APP: 2025-08-01 21:36:17,364 - llm_proxy.router - INFO - [1754055377364-3502] Processing POST v1/chat/completions
2025-08-01 21:36:17,364 - LLMProxyService - INFO - APP: 2025-08-01 21:36:17,364 - llm_proxy.router - INFO - [1754055377364-3502] Detected format: openai (confidence: 0.74)
2025-08-01 21:36:17,619 - LLMProxyService - INFO - APP: 2025-08-01 21:36:17,618 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:36:17,619 - LLMProxyService - INFO - APP: 2025-08-01 21:36:17,619 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:36:18,625 - LLMProxyService - INFO - APP: 2025-08-01 21:36:18,625 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:36:18,879 - LLMProxyService - INFO - APP: 2025-08-01 21:36:18,879 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:36:18,880 - LLMProxyService - INFO - APP: 2025-08-01 21:36:18,880 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:36:20,890 - LLMProxyService - INFO - APP: 2025-08-01 21:36:20,890 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:36:21,144 - LLMProxyService - INFO - APP: 2025-08-01 21:36:21,143 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:36:21,145 - LLMProxyService - INFO - APP: 2025-08-01 21:36:21,145 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:36:21,145 - LLMProxyService - INFO - APP: 2025-08-01 21:36:21,145 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:36:21,145 - LLMProxyService - INFO - APP: 2025-08-01 21:36:21,145 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:36:21,145 - LLMProxyService - INFO - APP: 2025-08-01 21:36:21,145 - llm_proxy.router - INFO - [1754055377364-3502] Request processed in 3780.9ms
2025-08-01 21:36:21,145 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:56950 - "POST /v1/chat/completions HTTP/1.1" 500 Internal Server Error
2025-08-01 21:37:25,523 - LLMProxyService - INFO - 正在停止 LLM 代理服务...
2025-08-01 21:37:25,523 - LLMProxyService - INFO - 收到停止信号
2025-08-01 21:37:25,523 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:37:25,540 - LLMProxyService - INFO - 服务进程已停止
2025-08-01 21:37:25,862 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:37:25,863 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:37:25,864 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:37:25,864 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:37:25,864 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:37:25,868 - LLMProxyService - INFO - 服务进程已启动，PID: 24732
2025-08-01 21:37:25,874 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:37:25,878 - LLMProxyService - INFO - 正在停止 LLM 代理服务...
2025-08-01 21:37:25,878 - LLMProxyService - INFO - 收到停止信号
2025-08-01 21:37:25,878 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:37:25,883 - LLMProxyService - INFO - 服务进程已停止
2025-08-01 21:37:28,516 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:37:28,518 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:37:28,518 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:37:28,518 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:37:28,518 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:37:28,522 - LLMProxyService - INFO - 服务进程已启动，PID: 14288
2025-08-01 21:37:28,532 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:37:31,700 - LLMProxyService - INFO - APP: 2025-08-01 21:37:31,700 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:37:34,168 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,168 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:37:34,170 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,170 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:37:34,170 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,170 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:37:34,170 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,170 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:37:34,170 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,170 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:37:34,170 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,170 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:37:34,201 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [14288]
2025-08-01 21:37:34,201 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:37:34,201 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,201 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,201 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,201 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,202 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,202 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,202 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:37:34,202 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:37:34,202 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,202 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:37:34,202 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
2025-08-01 21:37:34,509 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:59154 - "GET /health HTTP/1.1" 200 OK
2025-08-01 21:37:34,514 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,514 - llm_proxy.router - INFO - [1754055454514-2314] Processing POST v1/chat/completions
2025-08-01 21:37:34,515 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,515 - llm_proxy.router - INFO - [1754055454514-2314] Detected format: openai (confidence: 0.74)
2025-08-01 21:37:34,772 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,772 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:37:34,772 - LLMProxyService - INFO - APP: 2025-08-01 21:37:34,772 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:37:35,788 - LLMProxyService - INFO - APP: 2025-08-01 21:37:35,788 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:37:36,040 - LLMProxyService - INFO - APP: 2025-08-01 21:37:36,040 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:37:36,041 - LLMProxyService - INFO - APP: 2025-08-01 21:37:36,041 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:37:38,045 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,045 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:37:38,318 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,318 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:37:38,319 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,319 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:37:38,319 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,319 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:37:38,319 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,319 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:37:38,319 - LLMProxyService - INFO - APP: 2025-08-01 21:37:38,319 - llm_proxy.router - INFO - [1754055454514-2314] Request processed in 3805.3ms
2025-08-01 21:37:38,319 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:59160 - "POST /v1/chat/completions HTTP/1.1" 500 Internal Server Error
2025-08-01 21:38:24,408 - LLMProxyService - INFO - 正在停止 LLM 代理服务...
2025-08-01 21:38:24,409 - LLMProxyService - INFO - 收到停止信号
2025-08-01 21:38:24,409 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:38:24,429 - LLMProxyService - INFO - 服务进程已停止
2025-08-01 21:38:25,590 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:38:25,591 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:38:25,591 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:38:25,591 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:38:25,591 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:38:25,593 - LLMProxyService - INFO - 服务进程已启动，PID: 27096
2025-08-01 21:38:25,594 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:38:28,855 - LLMProxyService - INFO - APP: 2025-08-01 21:38:28,854 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,095 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,095 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,096 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,096 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,096 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,096 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:38:31,096 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,096 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:38:31,097 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,097 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:38:31,097 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,097 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:38:31,097 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,097 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:38:31,097 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,097 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:38:31,097 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,097 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:38:31,128 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [27096]
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:38:31,128 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:38:31,128 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:38:31,128 - LLMProxyService - INFO - APP: 2025-08-01 21:38:31,128 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:38:31,129 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
2025-08-01 21:38:40,566 - LLMProxyService - INFO - 正在停止 LLM 代理服务...
2025-08-01 21:38:40,566 - LLMProxyService - INFO - 收到停止信号
2025-08-01 21:38:40,567 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:38:40,584 - LLMProxyService - INFO - 服务进程已停止
2025-08-01 21:38:41,564 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:38:41,565 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:38:41,565 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:38:41,565 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:38:41,565 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:38:41,568 - LLMProxyService - INFO - 服务进程已启动，PID: 28152
2025-08-01 21:38:41,569 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:38:44,766 - LLMProxyService - INFO - APP: 2025-08-01 21:38:44,766 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:38:46,739 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,739 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:38:46,740 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,740 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:38:46,740 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,740 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:38:46,740 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,740 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:38:46,740 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,740 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:38:46,740 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,740 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:38:46,771 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [28152]
2025-08-01 21:38:46,772 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:38:46,772 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:38:46,772 - LLMProxyService - INFO - APP: 2025-08-01 21:38:46,772 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:38:46,772 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
2025-08-01 21:38:56,163 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:61493 - "GET /health HTTP/1.1" 200 OK
2025-08-01 21:38:56,169 - LLMProxyService - INFO - APP: 2025-08-01 21:38:56,169 - llm_proxy.router - INFO - [1754055536169-865] Processing POST v1/chat/completions
2025-08-01 21:38:56,170 - LLMProxyService - INFO - APP: 2025-08-01 21:38:56,170 - llm_proxy.router - INFO - [1754055536169-865] Detected format: openai (confidence: 0.74)
2025-08-01 21:38:56,439 - LLMProxyService - INFO - APP: 2025-08-01 21:38:56,438 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:38:56,439 - LLMProxyService - INFO - APP: 2025-08-01 21:38:56,438 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:38:57,452 - LLMProxyService - INFO - APP: 2025-08-01 21:38:57,452 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:38:57,705 - LLMProxyService - INFO - APP: 2025-08-01 21:38:57,705 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:38:57,706 - LLMProxyService - INFO - APP: 2025-08-01 21:38:57,706 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:38:59,715 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,715 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:38:59,969 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,969 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:38:59,970 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,970 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:38:59,970 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,970 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:38:59,970 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,970 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:38:59,970 - LLMProxyService - INFO - APP: 2025-08-01 21:38:59,970 - llm_proxy.router - INFO - [1754055536169-865] Request processed in 3801.7ms
2025-08-01 21:38:59,970 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:61494 - "POST /v1/chat/completions HTTP/1.1" 500 Internal Server Error
2025-08-01 21:40:12,607 - LLMProxyService - INFO - APP: 2025-08-01 21:40:12,607 - llm_proxy.router - INFO - [1754055612607-865] Processing POST v1/chat/completions
2025-08-01 21:40:12,607 - LLMProxyService - INFO - APP: 2025-08-01 21:40:12,607 - llm_proxy.router - INFO - [1754055612607-865] Detected format: openai (confidence: 0.74)
2025-08-01 21:40:12,862 - LLMProxyService - INFO - APP: 2025-08-01 21:40:12,862 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:40:12,862 - LLMProxyService - INFO - APP: 2025-08-01 21:40:12,862 - llm_proxy.proxy_forwarder - WARNING - Request attempt 1 failed: Too little data for declared Content-Length
2025-08-01 21:40:13,864 - LLMProxyService - INFO - APP: 2025-08-01 21:40:13,864 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 2/3)
2025-08-01 21:40:14,116 - LLMProxyService - INFO - APP: 2025-08-01 21:40:14,116 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:40:14,118 - LLMProxyService - INFO - APP: 2025-08-01 21:40:14,117 - llm_proxy.proxy_forwarder - WARNING - Request attempt 2 failed: Too little data for declared Content-Length
2025-08-01 21:40:16,123 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,123 - llm_proxy.proxy_forwarder - INFO - Retrying request (attempt 3/3)
2025-08-01 21:40:16,376 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,376 - llm_proxy.proxy_forwarder - ERROR - Unexpected error during request forwarding: Too little data for declared Content-Length
2025-08-01 21:40:16,377 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,377 - llm_proxy.proxy_forwarder - WARNING - Request attempt 3 failed: Too little data for declared Content-Length
2025-08-01 21:40:16,377 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,377 - llm_proxy.proxy_forwarder - ERROR - All 3 attempts failed
2025-08-01 21:40:16,377 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,377 - llm_proxy.proxy_forwarder - ERROR - Request forwarding failed: Too little data for declared Content-Length
2025-08-01 21:40:16,377 - LLMProxyService - INFO - APP: 2025-08-01 21:40:16,377 - llm_proxy.router - INFO - [1754055612607-865] Request processed in 3770.2ms
2025-08-01 21:40:16,378 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:63706 - "POST /v1/chat/completions HTTP/1.1" 500 Internal Server Error
2025-08-01 21:41:48,344 - LLMProxyService - INFO - 正在停止 LLM 代理服务...
2025-08-01 21:41:48,345 - LLMProxyService - INFO - 收到停止信号
2025-08-01 21:41:48,345 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:41:48,362 - LLMProxyService - INFO - 服务进程已停止
2025-08-01 21:41:48,473 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:41:48,473 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:41:48,473 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:41:48,474 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:41:48,474 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:41:48,476 - LLMProxyService - INFO - 服务进程已启动，PID: 25956
2025-08-01 21:41:48,476 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:41:51,382 - LLMProxyService - INFO - APP: 2025-08-01 21:41:51,381 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:41:53,599 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,599 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:41:53,601 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,601 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:41:53,601 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,601 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:41:53,601 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,601 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:41:53,601 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,601 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:41:53,601 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,601 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:41:53,636 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [25956]
2025-08-01 21:41:53,636 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:41:53,636 - LLMProxyService - INFO - APP: 2025-08-01 21:41:53,636 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:41:53,636 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:41:53,637 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
2025-08-01 21:41:54,925 - LLMProxyService - INFO - APP: 2025-08-01 21:41:54,925 - llm_proxy.router - INFO - [1754055714925-1600] Processing POST v1/chat/completions
2025-08-01 21:41:54,927 - LLMProxyService - INFO - APP: 2025-08-01 21:41:54,927 - llm_proxy.router - INFO - [1754055714925-1600] Detected format: openai (confidence: 0.74)
2025-08-01 21:41:57,504 - LLMProxyService - INFO - APP: 2025-08-01 21:41:57,504 - httpx - INFO - HTTP Request: POST http://127.0.0.1:8001/v1/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 21:41:57,505 - LLMProxyService - INFO - APP: 2025-08-01 21:41:57,505 - llm_proxy.router - INFO - [1754055714925-1600] Request processed in 2579.5ms
2025-08-01 21:41:57,505 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:3159 - "POST /v1/chat/completions HTTP/1.1" 200 OK
2025-08-01 21:42:19,707 - LLMProxyService - INFO - APP: 2025-08-01 21:42:19,707 - llm_proxy.router - INFO - [1754055739707-7724] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:42:19,708 - LLMProxyService - INFO - APP: 2025-08-01 21:42:19,708 - llm_proxy.router - INFO - [1754055739707-7724] Detected format: gemini (confidence: 1.00)
2025-08-01 21:42:19,972 - LLMProxyService - INFO - APP: 2025-08-01 21:42:19,972 - httpx - INFO - HTTP Request: POST http://127.0.0.1:8001/v1/models/gemini-2.5-flash:streamGenerateContent?key=3 "HTTP/1.1 422 Unprocessable Content"
2025-08-01 21:42:19,973 - LLMProxyService - INFO - APP: 2025-08-01 21:42:19,973 - llm_proxy.router - INFO - [1754055739707-7724] Request processed in 265.4ms
2025-08-01 21:42:19,973 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:3853 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 422 Unprocessable Content
2025-08-01 21:42:27,626 - LLMProxyService - INFO - APP: 2025-08-01 21:42:27,626 - llm_proxy.router - INFO - [1754055747626-7724] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:42:27,626 - LLMProxyService - INFO - APP: 2025-08-01 21:42:27,626 - llm_proxy.router - INFO - [1754055747626-7724] Detected format: gemini (confidence: 1.00)
2025-08-01 21:42:27,885 - LLMProxyService - INFO - APP: 2025-08-01 21:42:27,885 - httpx - INFO - HTTP Request: POST http://127.0.0.1:8001/v1/models/gemini-2.5-flash:streamGenerateContent?key=3 "HTTP/1.1 422 Unprocessable Content"
2025-08-01 21:42:27,885 - LLMProxyService - INFO - APP: 2025-08-01 21:42:27,885 - llm_proxy.router - INFO - [1754055747626-7724] Request processed in 259.2ms
2025-08-01 21:42:27,885 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:4085 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 422 Unprocessable Content
2025-08-01 21:44:05,252 - LLMProxyService - INFO - APP: 2025-08-01 21:44:05,252 - llm_proxy.router - INFO - [1754055845252-8377] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:44:05,252 - LLMProxyService - INFO - APP: 2025-08-01 21:44:05,252 - llm_proxy.router - INFO - [1754055845252-8377] Detected format: gemini (confidence: 1.00)
2025-08-01 21:44:05,513 - LLMProxyService - INFO - APP: 2025-08-01 21:44:05,513 - httpx - INFO - HTTP Request: POST http://127.0.0.1:8001/v1/models/gemini-2.5-flash:streamGenerateContent?key=3 "HTTP/1.1 422 Unprocessable Content"
2025-08-01 21:44:05,514 - LLMProxyService - INFO - APP: 2025-08-01 21:44:05,514 - llm_proxy.router - INFO - [1754055845252-8377] Request processed in 262.2ms
2025-08-01 21:44:05,514 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:6986 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 422 Unprocessable Content
2025-08-01 21:44:09,478 - LLMProxyService - INFO - APP: 2025-08-01 21:44:09,478 - llm_proxy.router - INFO - [1754055849477-8377] Processing POST models/gemini-2.5-flash:streamGenerateContent
2025-08-01 21:44:09,478 - LLMProxyService - INFO - APP: 2025-08-01 21:44:09,478 - llm_proxy.router - INFO - [1754055849477-8377] Detected format: gemini (confidence: 1.00)
2025-08-01 21:44:09,740 - LLMProxyService - INFO - APP: 2025-08-01 21:44:09,740 - httpx - INFO - HTTP Request: POST http://127.0.0.1:8001/v1/models/gemini-2.5-flash:streamGenerateContent?key=3 "HTTP/1.1 422 Unprocessable Content"
2025-08-01 21:44:09,740 - LLMProxyService - INFO - APP: 2025-08-01 21:44:09,740 - llm_proxy.router - INFO - [1754055849477-8377] Request processed in 262.9ms
2025-08-01 21:44:09,741 - LLMProxyService - INFO - APP: INFO:     127.0.0.1:7106 - "POST /models/gemini-2.5-flash%3AstreamGenerateContent?key=3 HTTP/1.1" 422 Unprocessable Content
