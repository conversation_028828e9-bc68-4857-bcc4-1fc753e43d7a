2025-08-01 21:30:19,905 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:30:19,953 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:30:19,953 - LLMProxyService - INFO - 启动命令: py main.py
2025-08-01 21:30:19,956 - LLMProxyService - ERROR - 服务运行时出错: [WinError 2] 系统找不到指定的文件。
2025-08-01 21:32:13,427 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:32:13,428 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:32:13,429 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonservice.exe
2025-08-01 21:32:13,429 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonservice.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:32:13,431 - LLMProxyService - INFO - 服务进程已启动，PID: 28916
2025-08-01 21:32:13,432 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: C : \ U s e r s \ A d m i n i s t r a t o r \ A p p D a t a \ L o c a l \ P r o g r a m s \ P y t h o n \ P y t h o n 3 1 3 \ p y t h o n s e r v i c e . e x e   -   P y t h o n   S e r v i c e   M a n a g e r 
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP:  Options:
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: -debug servicename [parms] - debug the Python service.
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: 
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: NOTE: You do not start the service using this program - start the
2025-08-01 21:32:13,480 - LLMProxyService - INFO - APP: service using Control Panel, or 'net start service_name'
2025-08-01 21:32:14,432 - LLMProxyService - ERROR - 服务进程意外结束，返回码: 2
2025-08-01 21:32:14,432 - LLMProxyService - INFO - LLM 代理服务已停止
2025-08-01 21:33:37,387 - LLMProxyService - INFO - 正在启动 LLM 代理服务...
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 工作目录: C:\Users\<USER>\llm_proxy_server
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 替换为标准Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:33:37,388 - LLMProxyService - INFO - Python解释器: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
2025-08-01 21:33:37,388 - LLMProxyService - INFO - 启动命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\llm_proxy_server\main.py
2025-08-01 21:33:37,391 - LLMProxyService - INFO - 服务进程已启动，PID: 6288
2025-08-01 21:33:37,391 - LLMProxyService - INFO - LLM 代理服务启动成功
2025-08-01 21:33:40,525 - LLMProxyService - INFO - APP: 2025-08-01 21:33:40,525 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-01 21:33:42,931 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.format_detector - INFO - Format detector initialized with comprehensive detection rules
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.format_detector - INFO - Cached format detector initialized with cache size: 1000
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.litellm_converter - INFO - LiteLLM configuration applied successfully
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,931 - llm_proxy.litellm_converter - INFO - LiteLLM Converter initialized successfully
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.proxy_forwarder - INFO - Proxy client initialized with base_url: http://127.0.0.1:8001/v1, timeout: 300s
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.proxy_forwarder - INFO - Proxy forwarder initialized with target: http://127.0.0.1:8001/v1
2025-08-01 21:33:42,932 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,932 - llm_proxy.router - INFO - Proxy router initialized with core services
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.application - INFO - Routers configured successfully
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.application - INFO - FastAPI application created successfully
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Starting LLM Proxy Server...
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Server will listen on 0.0.0.0:8002
2025-08-01 21:33:42,933 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,933 - llm_proxy.main - INFO - Target server: 127.0.0.1:8001
2025-08-01 21:33:42,968 - LLMProxyService - ERROR - APP_ERROR: INFO:     Started server process [6288]
2025-08-01 21:33:42,968 - LLMProxyService - ERROR - APP_ERROR: INFO:     Waiting for application startup.
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO - LLM Proxy Server starting up...
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO - Proxy server configuration:
2025-08-01 21:33:42,968 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Listen on: 0.0.0.0:8002
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Target server: http://127.0.0.1:8001/v1
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Log level: INFO
2025-08-01 21:33:42,969 - LLMProxyService - ERROR - APP_ERROR: INFO:     Application startup complete.
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,968 - llm_proxy.application - INFO -   - Max concurrent requests: 100
2025-08-01 21:33:42,969 - LLMProxyService - INFO - APP: 2025-08-01 21:33:42,969 - llm_proxy.application - INFO -   - Request timeout: 300s
2025-08-01 21:33:42,969 - LLMProxyService - ERROR - APP_ERROR: INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
