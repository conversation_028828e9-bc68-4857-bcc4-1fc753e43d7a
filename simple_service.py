#!/usr/bin/env python3
"""
简化的 Windows 服务包装器
使用 py 命令启动 LLM 代理服务器
"""

import sys
import os
import time
import logging
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
except ImportError:
    print("错误: 需要安装 pywin32 库")
    print("请运行: py -m pip install pywin32")
    sys.exit(1)


class LLMProxyService(win32serviceutil.ServiceFramework):
    """LLM 代理服务器 Windows 服务类"""
    
    # 服务配置
    _svc_name_ = "LLMProxyServer"
    _svc_display_name_ = "LLM Format Proxy Server"
    _svc_description_ = "基于 LiteLLM 的智能 LLM 格式转换代理服务器"
    
    def __init__(self, args):
        """初始化服务"""
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.logger = self._setup_service_logger()
        self.process = None
        
    def _setup_service_logger(self):
        """设置服务日志"""
        logger = logging.getLogger('LLMProxyService')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "service.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
        
    def SvcStop(self):
        """停止服务"""
        self.logger.info("正在停止 LLM 代理服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        
        # 停止子进程
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                self.logger.info("服务进程已停止")
            except Exception as e:
                self.logger.error(f"停止服务进程时出错: {e}")
                try:
                    self.process.kill()
                except:
                    pass
                
    def SvcDoRun(self):
        """运行服务"""
        try:
            self.logger.info("正在启动 LLM 代理服务...")
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
            
            # 切换到项目目录
            os.chdir(project_root)
            
            self.logger.info(f"工作目录: {project_root}")
            
            # 获取正确的Python解释器路径
            python_exe = sys.executable

            # 如果是 pythonservice.exe，替换为 python.exe
            if python_exe and "pythonservice.exe" in python_exe:
                python_exe = python_exe.replace("pythonservice.exe", "python.exe")
                self.logger.info(f"替换为标准Python解释器: {python_exe}")

            # 验证Python解释器是否存在
            if not python_exe or not os.path.exists(python_exe):
                # 尝试查找Python 3.13
                possible_paths = [
                    r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe",
                    r"C:\Python313\python.exe",
                    r"C:\Program Files\Python313\python.exe",
                    "py"  # 最后的备用方案
                ]

                for path in possible_paths:
                    if path == "py" or os.path.exists(path):
                        python_exe = path
                        self.logger.info(f"找到Python解释器: {python_exe}")
                        break
                else:
                    raise Exception("无法找到有效的Python解释器")

            # 使用完整路径启动主程序
            main_script = project_root / "main.py"
            cmd = [python_exe, str(main_script)]

            self.logger.info(f"Python解释器: {python_exe}")
            self.logger.info(f"启动命令: {' '.join(cmd)}")

            # 启动子进程
            self.process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.logger.info(f"服务进程已启动，PID: {self.process.pid}")
            
            # 监控进程输出
            import threading
            
            def log_output():
                """记录进程输出"""
                try:
                    while self.process.poll() is None:
                        line = self.process.stdout.readline()
                        if line:
                            self.logger.info(f"APP: {line.strip()}")
                except Exception as e:
                    self.logger.error(f"读取进程输出时出错: {e}")
            
            def log_error():
                """记录进程错误"""
                try:
                    while self.process.poll() is None:
                        line = self.process.stderr.readline()
                        if line:
                            self.logger.error(f"APP_ERROR: {line.strip()}")
                except Exception as e:
                    self.logger.error(f"读取进程错误时出错: {e}")
            
            # 启动输出监控线程
            output_thread = threading.Thread(target=log_output, daemon=True)
            error_thread = threading.Thread(target=log_error, daemon=True)
            output_thread.start()
            error_thread.start()
            
            self.logger.info("LLM 代理服务启动成功")
            
            # 等待停止信号或进程结束
            while True:
                # 检查停止信号
                wait_result = win32event.WaitForSingleObject(self.hWaitStop, 1000)  # 1秒超时
                if wait_result == win32event.WAIT_OBJECT_0:
                    # 收到停止信号
                    self.logger.info("收到停止信号")
                    break
                
                # 检查进程是否还在运行
                if self.process.poll() is not None:
                    # 进程已结束
                    return_code = self.process.returncode
                    self.logger.error(f"服务进程意外结束，返回码: {return_code}")
                    
                    # 读取剩余的错误输出
                    try:
                        stderr_output = self.process.stderr.read()
                        if stderr_output:
                            self.logger.error(f"进程错误输出: {stderr_output}")
                    except:
                        pass
                    
                    break
            
            self.logger.info("LLM 代理服务已停止")
            
        except Exception as e:
            self.logger.error(f"服务运行时出错: {e}")
            servicemanager.LogErrorMsg(f"LLM 代理服务错误: {e}")
            raise


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(LLMProxyService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        win32serviceutil.HandleCommandLine(LLMProxyService)


if __name__ == '__main__':
    main()
