#!/usr/bin/env python3
"""
直接测试目标服务器
"""

import requests
import json

def test_target_server():
    """直接测试目标服务器（端口8001）"""
    try:
        url = "http://127.0.0.1:8001/v1/chat/completions"
        headers = {
            "Authorization": "Bearer 3",
            "Content-Type": "application/json"
        }
        data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {"role": "user", "content": "Hello, this is a test message"}
            ],
            "stream": False
        }
        
        print(f"直接测试目标服务器: {url}")
        print(f"请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"响应内容: {response.json()}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"直接测试失败: {e}")
        return False

def test_proxy_server():
    """测试代理服务器（端口8002）"""
    try:
        url = "http://localhost:8002/v1/chat/completions"
        headers = {
            "Authorization": "Bearer 3",
            "Content-Type": "application/json"
        }
        data = {
            "model": "gemini-2.5-flash",
            "messages": [
                {"role": "user", "content": "Hello, this is a test message"}
            ],
            "stream": False
        }
        
        print(f"测试代理服务器: {url}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"代理响应状态码: {response.status_code}")
        print(f"代理响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"代理响应内容: {response.json()}")
            return True
        else:
            print(f"代理错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"代理测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("LLM 服务器连接测试")
    print("=" * 60)
    
    print("\n1. 测试目标服务器（端口8001）...")
    target_ok = test_target_server()
    
    print("\n" + "-" * 60)
    print("\n2. 测试代理服务器（端口8002）...")
    proxy_ok = test_proxy_server()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"目标服务器: {'✓ 通过' if target_ok else '✗ 失败'}")
    print(f"代理服务器: {'✓ 通过' if proxy_ok else '✗ 失败'}")
    print("=" * 60)

if __name__ == "__main__":
    main()
