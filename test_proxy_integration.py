#!/usr/bin/env python3
"""
代理转发服务集成测试脚本
测试与实际 HTTP 服务器的交互
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx

async def test_httpbin_integration():
    """测试与 httpbin.org 的集成"""
    print("Testing httpbin.org integration...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        # 使用 httpbin.org 作为测试目标
        forwarder = ProxyForwarder("https://httpbin.org")
        
        # 测试 GET 请求
        response = await forwarder.forward_request(
            method="GET",
            path="/get",
            params={"test": "value"}
        )
        
        if response.status_code == 200:
            print("✓ GET request to httpbin successful")
            
            # 解析响应
            try:
                data = json.loads(response.body)
                if "args" in data and data["args"].get("test") == "value":
                    print("✓ Query parameters forwarded correctly")
                else:
                    print("✗ Query parameters not forwarded correctly")
                    return False
            except json.JSONDecodeError:
                print("✗ Response is not valid JSON")
                return False
        else:
            print(f"✗ GET request failed with status: {response.status_code}")
            return False
        
        # 测试 POST 请求
        test_data = {"message": "Hello from proxy", "timestamp": time.time()}
        response = await forwarder.forward_request(
            method="POST",
            path="/post",
            headers={"Content-Type": "application/json"},
            data=test_data
        )
        
        if response.status_code == 200:
            print("✓ POST request to httpbin successful")
            
            # 验证数据转发
            try:
                data = json.loads(response.body)
                if "json" in data and data["json"]["message"] == test_data["message"]:
                    print("✓ POST data forwarded correctly")
                else:
                    print("✗ POST data not forwarded correctly")
                    return False
            except json.JSONDecodeError:
                print("✗ POST response is not valid JSON")
                return False
        else:
            print(f"✗ POST request failed with status: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ httpbin integration test failed: {e}")
        return False

async def test_error_handling():
    """测试错误处理"""
    print("\nTesting error handling...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        # 测试无效的目标服务器
        forwarder = ProxyForwarder("http://invalid-server-that-does-not-exist.com")
        
        try:
            response = await forwarder.forward_request(
                method="GET",
                path="/test"
            )
            print("✗ Should have failed for invalid server")
            return False
        except Exception:
            print("✓ Invalid server error handled correctly")
        
        # 测试超时处理
        forwarder_timeout = ProxyForwarder("https://httpbin.org")
        forwarder_timeout.client.timeout = 0.001  # 极短超时
        
        try:
            response = await forwarder_timeout.forward_request(
                method="GET",
                path="/delay/5"  # 5秒延迟
            )
            print("✗ Should have timed out")
            return False
        except Exception:
            print("✓ Timeout error handled correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False

async def test_concurrent_requests():
    """测试并发请求"""
    print("\nTesting concurrent requests...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("https://httpbin.org")
        
        # 创建多个并发请求
        async def make_request(request_id: int):
            response = await forwarder.forward_request(
                method="GET",
                path="/get",
                params={"request_id": str(request_id)}
            )
            return response.status_code == 200, request_id
        
        # 并发执行10个请求
        tasks = [make_request(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful_requests = 0
        for result in results:
            if isinstance(result, tuple) and result[0]:
                successful_requests += 1
            elif isinstance(result, Exception):
                print(f"✗ Request failed with exception: {result}")
        
        success_rate = successful_requests / len(tasks)
        print(f"✓ Concurrent requests: {successful_requests}/{len(tasks)} successful ({success_rate:.1%})")
        
        # 要求至少80%成功率
        if success_rate >= 0.8:
            print("✓ Concurrent request test passed")
            return True
        else:
            print("✗ Concurrent request success rate too low")
            return False
        
    except Exception as e:
        print(f"✗ Concurrent requests test failed: {e}")
        return False

async def test_performance():
    """测试性能"""
    print("\nTesting performance...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("https://httpbin.org")
        
        # 测试单个请求的延迟
        start_time = time.time()
        response = await forwarder.forward_request(
            method="GET",
            path="/get"
        )
        end_time = time.time()
        
        if response.status_code == 200:
            latency = (end_time - start_time) * 1000  # 转换为毫秒
            print(f"✓ Single request latency: {latency:.1f}ms")
            
            # 验证延迟是否合理（应该小于5秒，考虑网络延迟）
            if latency < 5000:
                print("✓ Latency within acceptable range")
            else:
                print("⚠ Latency higher than expected (but may be due to network)")
        else:
            print(f"✗ Performance test request failed: {response.status_code}")
            return False
        
        # 测试吞吐量（简单测试）
        start_time = time.time()
        num_requests = 5
        
        tasks = []
        for _ in range(num_requests):
            task = forwarder.forward_request("GET", "/get")
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        successful_responses = sum(1 for r in responses if hasattr(r, 'status_code') and r.status_code == 200)
        total_time = end_time - start_time
        throughput = successful_responses / total_time
        
        print(f"✓ Throughput test: {successful_responses}/{num_requests} requests in {total_time:.2f}s ({throughput:.1f} req/s)")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False

async def test_header_forwarding():
    """测试请求头转发"""
    print("\nTesting header forwarding...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("https://httpbin.org")
        
        # 测试自定义请求头
        custom_headers = {
            "X-Custom-Header": "test-value",
            "User-Agent": "LLM-Proxy-Test/1.0",
            "Authorization": "Bearer test-token"
        }
        
        response = await forwarder.forward_request(
            method="GET",
            path="/headers",
            headers=custom_headers
        )
        
        if response.status_code == 200:
            try:
                data = json.loads(response.body)
                received_headers = data.get("headers", {})
                
                # 检查自定义头部是否被转发
                if received_headers.get("X-Custom-Header") == "test-value":
                    print("✓ Custom header forwarded correctly")
                else:
                    print("✗ Custom header not forwarded correctly")
                    return False
                
                if received_headers.get("User-Agent") == "LLM-Proxy-Test/1.0":
                    print("✓ User-Agent header forwarded correctly")
                else:
                    print("✗ User-Agent header not forwarded correctly")
                    return False
                
                if received_headers.get("Authorization") == "Bearer test-token":
                    print("✓ Authorization header forwarded correctly")
                else:
                    print("✗ Authorization header not forwarded correctly")
                    return False
                
            except json.JSONDecodeError:
                print("✗ Headers response is not valid JSON")
                return False
        else:
            print(f"✗ Headers test failed with status: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Header forwarding test failed: {e}")
        return False

async def test_response_headers():
    """测试响应头处理"""
    print("\nTesting response headers...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder("https://httpbin.org")
        
        response = await forwarder.forward_request(
            method="GET",
            path="/response-headers",
            params={"X-Test-Response": "response-value"}
        )
        
        if response.status_code == 200:
            # 检查代理标识头部
            if "X-Proxy-Server" in response.headers:
                print("✓ Proxy identification header added")
            else:
                print("✗ Proxy identification header missing")
                return False
            
            # 检查内容类型头部
            if "content-type" in response.headers:
                print(f"✓ Content-Type header preserved: {response.headers['content-type']}")
            else:
                print("✗ Content-Type header missing")
                return False
            
        else:
            print(f"✗ Response headers test failed with status: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Response headers test failed: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - Proxy Integration Test")
    print("=" * 70)
    print("Note: These tests require internet connection to httpbin.org")
    
    tests = [
        ("httpbin.org Integration", test_httpbin_integration),
        ("Error Handling", test_error_handling),
        ("Concurrent Requests", test_concurrent_requests),
        ("Performance", test_performance),
        ("Header Forwarding", test_header_forwarding),
        ("Response Headers", test_response_headers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! Proxy forwarder is production ready.")
        return 0
    elif passed >= total * 0.8:  # 80% 通过率也可以接受（考虑网络问题）
        print("✅ Most integration tests passed. Service is likely working correctly.")
        print("Note: Some failures may be due to network connectivity issues.")
        return 0
    else:
        print("❌ Too many tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
