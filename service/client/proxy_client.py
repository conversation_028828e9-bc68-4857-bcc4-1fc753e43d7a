"""
代理客户端实现
继承现有 ApiClient 基类，提供统一的 HTTP 客户端接口
"""

import httpx
import random
import hashlib
from typing import Dict, Any, Optional, List, AsyncGenerator
from abc import ABC, abstractmethod

from config.settings import settings
from log.logger import get_proxy_forwarder_logger

logger = get_proxy_forwarder_logger()


class BaseProxyClient(ABC):
    """代理客户端基类，参考现有项目的 ApiClient 架构"""
    
    def __init__(self, base_url: str, timeout: int = None):
        """
        初始化代理客户端
        
        Args:
            base_url: 目标服务器基础 URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout or settings.REQUEST_TIMEOUT
        
        logger.info(f"Proxy client initialized with base_url: {self.base_url}, timeout: {self.timeout}s")
    
    def _select_proxy(self, api_key: Optional[str] = None) -> Optional[str]:
        """
        选择代理服务器（复用现有项目的代理选择逻辑）
        
        Args:
            api_key: API 密钥，用于一致性哈希
            
        Returns:
            选中的代理 URL，如果没有配置代理则返回 None
        """
        if not settings.USE_PROXY or not settings.PROXIES:
            return None
        
        # 一致性哈希选择代理
        if api_key and len(settings.PROXIES) > 1:
            proxy_index = hash(api_key) % len(settings.PROXIES)
            selected_proxy = settings.PROXIES[proxy_index]
            logger.debug(f"Selected proxy using consistency hash: {selected_proxy}")
            return selected_proxy
        
        # 随机选择代理
        selected_proxy = random.choice(settings.PROXIES)
        logger.debug(f"Selected proxy randomly: {selected_proxy}")
        return selected_proxy
    
    def _prepare_headers(self, original_headers: Dict[str, str] = None) -> Dict[str, str]:
        """
        准备请求头（复用现有项目的头部处理逻辑）
        
        Args:
            original_headers: 原始请求头
            
        Returns:
            处理后的请求头
        """
        headers = {}
        
        # 复制原始请求头
        if original_headers:
            headers.update(original_headers)
        
        # 添加自定义头部
        if hasattr(settings, 'CUSTOM_HEADERS') and settings.CUSTOM_HEADERS:
            headers.update(settings.CUSTOM_HEADERS)
            logger.debug(f"Added custom headers: {settings.CUSTOM_HEADERS}")
        
        return headers
    
    def _create_timeout(self) -> httpx.Timeout:
        """创建超时配置"""
        return httpx.Timeout(
            timeout=self.timeout,
            read=self.timeout,
            connect=30.0,  # 连接超时
            pool=10.0      # 连接池超时
        )
    
    @abstractmethod
    async def forward_request(
        self, 
        method: str, 
        path: str, 
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> httpx.Response:
        """转发普通请求"""
        pass
    
    @abstractmethod
    async def forward_stream_request(
        self, 
        method: str, 
        path: str, 
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> AsyncGenerator[bytes, None]:
        """转发流式请求"""
        pass


class ProxyClient(BaseProxyClient):
    """具体的代理客户端实现"""
    
    def __init__(self, base_url: str, timeout: int = None):
        super().__init__(base_url, timeout)
        self._client_pool = {}  # 客户端连接池
    
    async def forward_request(
        self, 
        method: str, 
        path: str, 
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> httpx.Response:
        """
        转发普通 HTTP 请求
        
        Args:
            method: HTTP 方法
            path: 请求路径
            headers: 请求头
            data: 请求数据
            params: 查询参数
            api_key: API 密钥（用于代理选择）
            
        Returns:
            HTTP 响应对象
        """
        try:
            # 构建完整 URL
            target_url = f"{self.base_url}{path}"
            
            # 选择代理
            proxy = self._select_proxy(api_key)
            
            # 准备请求头
            prepared_headers = self._prepare_headers(headers)
            
            # 创建超时配置
            timeout = self._create_timeout()
            
            logger.debug(f"Forwarding {method} request to {target_url}")
            if proxy:
                logger.debug(f"Using proxy: {proxy}")
            
            # 发送请求
            async with httpx.AsyncClient(
                timeout=timeout, 
                proxy=proxy,
                limits=httpx.Limits(
                    max_keepalive_connections=settings.CONNECTION_POOL_SIZE,
                    max_connections=settings.CONNECTION_POOL_SIZE * 2
                )
            ) as client:
                # 根据数据类型选择合适的参数
                request_kwargs = {
                    "method": method,
                    "url": target_url,
                    "headers": prepared_headers,
                    "params": params
                }

                if isinstance(data, dict):
                    request_kwargs["json"] = data
                elif isinstance(data, (str, bytes)):
                    request_kwargs["content"] = data
                elif data is not None:
                    # 其他类型的数据，尝试转换为JSON
                    import json
                    request_kwargs["content"] = json.dumps(data)
                    if "content-type" not in {k.lower() for k in prepared_headers.keys()}:
                        prepared_headers["Content-Type"] = "application/json"

                response = await client.request(**request_kwargs)
                
                logger.debug(f"Request completed with status: {response.status_code}")
                return response
                
        except httpx.TimeoutException as e:
            logger.error(f"Request timeout: {e}")
            raise Exception(f"Request timeout: {e}")
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"Request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during request forwarding: {e}")
            raise
    
    async def forward_stream_request(
        self, 
        method: str, 
        path: str, 
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> AsyncGenerator[bytes, None]:
        """
        转发流式 HTTP 请求
        
        Args:
            method: HTTP 方法
            path: 请求路径
            headers: 请求头
            data: 请求数据
            params: 查询参数
            api_key: API 密钥（用于代理选择）
            
        Yields:
            响应数据块
        """
        try:
            # 构建完整 URL
            target_url = f"{self.base_url}{path}"
            
            # 选择代理
            proxy = self._select_proxy(api_key)
            
            # 准备请求头
            prepared_headers = self._prepare_headers(headers)
            
            # 创建超时配置
            timeout = self._create_timeout()
            
            logger.debug(f"Forwarding {method} stream request to {target_url}")
            if proxy:
                logger.debug(f"Using proxy: {proxy}")
            
            # 发送流式请求
            async with httpx.AsyncClient(
                timeout=timeout, 
                proxy=proxy,
                limits=httpx.Limits(
                    max_keepalive_connections=settings.CONNECTION_POOL_SIZE,
                    max_connections=settings.CONNECTION_POOL_SIZE * 2
                )
            ) as client:
                async with client.stream(
                    method=method,
                    url=target_url,
                    headers=prepared_headers,
                    json=data if isinstance(data, dict) else None,
                    content=data if isinstance(data, (str, bytes)) else None,
                    params=params
                ) as response:
                    # 检查响应状态
                    if response.status_code >= 400:
                        error_content = await response.aread()
                        error_msg = error_content.decode("utf-8")
                        logger.error(f"Stream request failed with status {response.status_code}: {error_msg}")
                        raise Exception(f"Stream request failed with status {response.status_code}: {error_msg}")
                    
                    logger.debug(f"Stream request started with status: {response.status_code}")
                    
                    # 流式读取响应数据
                    async for chunk in response.aiter_bytes():
                        if chunk:
                            yield chunk
                            
        except httpx.TimeoutException as e:
            logger.error(f"Stream request timeout: {e}")
            raise Exception(f"Stream request timeout: {e}")
        except httpx.RequestError as e:
            logger.error(f"Stream request error: {e}")
            raise Exception(f"Stream request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during stream request forwarding: {e}")
            raise
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            服务是否健康
        """
        try:
            response = await self.forward_request("GET", "/health")
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取客户端统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "base_url": self.base_url,
            "timeout": self.timeout,
            "proxy_enabled": settings.USE_PROXY,
            "proxy_count": len(settings.PROXIES) if settings.PROXIES else 0,
            "connection_pool_size": settings.CONNECTION_POOL_SIZE
        }
