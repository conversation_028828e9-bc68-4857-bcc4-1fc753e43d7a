"""
LiteLLM 转换服务核心实现
基于 LiteLLM 库实现各种 LLM 格式的转换功能
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, AsyncGenerator, Union, List
import traceback

try:
    import litellm
    from litellm import completion, acompletion, embedding, aembedding
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False

from models.conversion_models import (
    LLMFormat, ChatCompletionRequest, EmbeddingRequest, ImageGenerationRequest,
    ConversionResult, ConversionError, UnsupportedFormatError, InvalidRequestError,
    LiteLLMConfig, CompletionResponse, EmbeddingResponse, StreamChunk
)
from log.logger import get_litellm_converter_logger
from config.settings import settings

logger = get_litellm_converter_logger()


class LiteLLMConverter:
    """LiteLLM 转换服务类"""
    
    def __init__(self, config: Optional[LiteLLMConfig] = None):
        """
        初始化 LiteLLM 转换器
        
        Args:
            config: LiteLLM 配置对象
        """
        if not LITELLM_AVAILABLE:
            raise ImportError("LiteLLM library is not installed. Please install it with: pip install litellm")
        
        self.config = config or LiteLLMConfig()
        self._setup_litellm()
        
        # 格式转换映射
        self.format_converters = {
            LLMFormat.GEMINI: self._convert_gemini_to_openai,
            LLMFormat.CLAUDE: self._convert_claude_to_openai,
            LLMFormat.ANTHROPIC: self._convert_anthropic_to_openai,
            LLMFormat.COHERE: self._convert_cohere_to_openai,
            LLMFormat.HUGGINGFACE: self._convert_huggingface_to_openai,
            LLMFormat.OLLAMA: self._convert_ollama_to_openai,
        }
        
        logger.info("LiteLLM Converter initialized successfully")
    
    def _setup_litellm(self):
        """设置 LiteLLM 配置"""
        try:
            # 设置全局配置
            if self.config.api_key:
                litellm.api_key = self.config.api_key
            
            if self.config.base_url:
                litellm.api_base = self.config.base_url
            
            # 设置超时
            litellm.request_timeout = self.config.timeout or settings.REQUEST_TIMEOUT
            
            # 设置重试次数
            litellm.num_retries = self.config.max_retries or 3
            
            # 设置自定义头部
            if self.config.custom_headers:
                litellm.headers = self.config.custom_headers
            
            # 设置代理
            if self.config.proxy:
                litellm.proxy = self.config.proxy
            
            # 启用详细日志（开发模式）
            if settings.LOG_LEVEL == "DEBUG":
                litellm.set_verbose = True
            
            logger.info("LiteLLM configuration applied successfully")
            
        except Exception as e:
            logger.error(f"Failed to setup LiteLLM: {str(e)}")
            raise ConversionError(f"LiteLLM setup failed: {str(e)}")
    
    async def convert_to_openai_format(
        self,
        request_data: Dict[str, Any],
        source_format: LLMFormat,
        request_path: str = None
    ) -> ConversionResult:
        """
        将请求数据转换为 OpenAI 格式
        
        Args:
            request_data: 原始请求数据
            source_format: 源格式类型
            
        Returns:
            转换结果
        """
        try:
            logger.debug(f"Converting from {source_format} to OpenAI format")
            
            if source_format == LLMFormat.OPENAI:
                # 已经是 OpenAI 格式，直接返回
                return ConversionResult(
                    success=True,
                    converted_data=request_data,
                    original_format=source_format,
                    target_format=LLMFormat.OPENAI
                )
            
            # 获取对应的转换器
            converter = self.format_converters.get(source_format)
            if not converter:
                raise UnsupportedFormatError(
                    f"Unsupported source format: {source_format}",
                    source_format=source_format.value,
                    target_format=LLMFormat.OPENAI.value
                )
            
            # 执行转换，传递路径信息用于提取模型名称
            if source_format == LLMFormat.GEMINI and request_path:
                converted_data = await converter(request_data, request_path)
            else:
                converted_data = await converter(request_data)
            
            return ConversionResult(
                success=True,
                converted_data=converted_data,
                original_format=source_format,
                target_format=LLMFormat.OPENAI,
                metadata={"conversion_method": converter.__name__}
            )
            
        except Exception as e:
            logger.error(f"Conversion failed: {str(e)}")
            return ConversionResult(
                success=False,
                error_message=str(e),
                original_format=source_format,
                target_format=LLMFormat.OPENAI
            )
    
    async def process_chat_completion(
        self, 
        request_data: Dict[str, Any], 
        stream: bool = False
    ) -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """
        处理聊天完成请求
        
        Args:
            request_data: OpenAI 格式的请求数据
            stream: 是否流式响应
            
        Returns:
            响应数据或流式生成器
        """
        try:
            logger.debug(f"Processing chat completion, stream={stream}")
            
            # 验证请求数据
            self._validate_chat_request(request_data)
            
            if stream:
                return self._process_stream_completion(request_data)
            else:
                return await self._process_normal_completion(request_data)
                
        except Exception as e:
            logger.error(f"Chat completion processing failed: {str(e)}")
            raise ConversionError(f"Chat completion failed: {str(e)}")
    
    async def process_embedding(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理嵌入请求
        
        Args:
            request_data: OpenAI 格式的嵌入请求数据
            
        Returns:
            嵌入响应数据
        """
        try:
            logger.debug("Processing embedding request")
            
            # 使用 LiteLLM 处理嵌入
            response = await aembedding(**request_data)
            
            logger.debug("Embedding processing completed")
            return response
            
        except Exception as e:
            logger.error(f"Embedding processing failed: {str(e)}")
            raise ConversionError(f"Embedding failed: {str(e)}")
    
    async def _process_normal_completion(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理非流式聊天完成"""
        try:
            # 使用 LiteLLM 异步完成
            response = await acompletion(**request_data)
            
            logger.debug("Normal completion processing completed")
            return response
            
        except Exception as e:
            logger.error(f"Normal completion failed: {str(e)}")
            raise ConversionError(f"Completion failed: {str(e)}")
    
    async def _process_stream_completion(self, request_data: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """处理流式聊天完成"""
        try:
            # 设置流式参数
            request_data["stream"] = True
            
            # 使用 LiteLLM 流式完成
            response = await acompletion(**request_data)
            
            async for chunk in response:
                if chunk:
                    # 转换为 JSON 字符串
                    chunk_json = json.dumps(chunk, ensure_ascii=False)
                    yield f"data: {chunk_json}\n\n"
            
            # 发送结束标记
            yield "data: [DONE]\n\n"
            
            logger.debug("Stream completion processing completed")
            
        except Exception as e:
            logger.error(f"Stream completion failed: {str(e)}")
            # 发送错误信息
            error_chunk = {
                "error": {
                    "message": str(e),
                    "type": "conversion_error"
                }
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"
    
    def _validate_chat_request(self, request_data: Dict[str, Any]):
        """验证聊天请求数据"""
        required_fields = ["model", "messages"]
        for field in required_fields:
            if field not in request_data:
                raise InvalidRequestError(f"Missing required field: {field}")
        
        if not isinstance(request_data["messages"], list):
            raise InvalidRequestError("Messages must be a list")
        
        if len(request_data["messages"]) == 0:
            raise InvalidRequestError("Messages list cannot be empty")
    
    # 格式转换方法
    async def _convert_gemini_to_openai(self, data: Dict[str, Any], request_path: str = None) -> Dict[str, Any]:
        """将 Gemini 格式转换为 OpenAI 格式"""
        logger.debug("Converting Gemini format to OpenAI")
        logger.debug(f"Input Gemini data: {json.dumps(data, indent=2)}")

        # 从URL路径中提取模型名称（如果有的话）
        model_name = "gemini-2.5-flash"  # 默认模型

        # 从请求路径中提取模型名称，例如：models/gemini-2.5-flash:generateContent
        if request_path and "models/" in request_path:
            try:
                # 提取 models/MODEL_NAME:operation 中的 MODEL_NAME
                path_parts = request_path.split("/")
                for part in path_parts:
                    if ":" in part:
                        model_name = part.split(":")[0]
                        break
                logger.debug(f"Extracted model name from path: {model_name}")
            except Exception as e:
                logger.warning(f"Failed to extract model name from path {request_path}: {e}")

        # 如果数据中有模型名称，优先使用
        if "model" in data:
            model_name = data["model"]

        # 基本的 Gemini 到 OpenAI 转换逻辑
        openai_data = {
            "model": model_name,
            "messages": [],
            "stream": data.get("stream", False)
        }

        # 处理生成配置
        generation_config = data.get("generationConfig", {})
        if generation_config.get("temperature") is not None:
            openai_data["temperature"] = generation_config["temperature"]
        if generation_config.get("maxOutputTokens") is not None:
            openai_data["max_tokens"] = generation_config["maxOutputTokens"]
        if generation_config.get("topP") is not None:
            openai_data["top_p"] = generation_config["topP"]

        # 转换消息格式
        if "contents" in data:
            for i, content in enumerate(data["contents"]):
                # Gemini格式中，第一个content通常是用户消息，后续可能是助手回复
                # 如果没有明确指定role，根据位置推断
                role = content.get("role")
                if not role:
                    # 默认第一个是用户消息，后续交替
                    role = "user" if i % 2 == 0 else "assistant"

                message_content = ""

                if "parts" in content:
                    for part in content["parts"]:
                        if "text" in part:
                            message_content += part["text"]
                        # 可以扩展支持其他类型的part（如图片等）

                if message_content.strip():  # 只添加非空消息
                    openai_data["messages"].append({
                        "role": role,
                        "content": message_content.strip()
                    })

        # 确保至少有一条消息
        if not openai_data["messages"]:
            raise InvalidRequestError("No valid messages found in Gemini request")

        logger.debug(f"Converted to OpenAI format: {json.dumps(openai_data, indent=2)}")
        return openai_data

    async def _convert_claude_to_openai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将 Claude/Anthropic 格式转换为 OpenAI 格式"""
        logger.debug("Converting Claude format to OpenAI")

        openai_data = {
            "model": data.get("model", "claude-3-sonnet-20240229"),
            "messages": [],
            "temperature": data.get("temperature", 1.0),
            "max_tokens": data.get("max_tokens", 4096),
            "top_p": data.get("top_p", 1.0),
        }

        # 转换消息格式
        if "messages" in data:
            for message in data["messages"]:
                openai_data["messages"].append({
                    "role": message.get("role", "user"),
                    "content": message.get("content", "")
                })

        return openai_data

    async def _convert_anthropic_to_openai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将 Anthropic 格式转换为 OpenAI 格式"""
        logger.debug("Converting Anthropic format to OpenAI")
        return await self._convert_claude_to_openai(data)

    async def _convert_cohere_to_openai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将 Cohere 格式转换为 OpenAI 格式"""
        logger.debug("Converting Cohere format to OpenAI")

        openai_data = {
            "model": data.get("model", "command"),
            "messages": [
                {
                    "role": "user",
                    "content": data.get("message", data.get("prompt", ""))
                }
            ],
            "temperature": data.get("temperature", 1.0),
            "max_tokens": data.get("max_tokens", 4096),
        }

        # 处理聊天历史
        if "chat_history" in data:
            messages = []
            for chat in data["chat_history"]:
                messages.append({
                    "role": "user" if chat.get("user_name") else "assistant",
                    "content": chat.get("message", "")
                })
            messages.append(openai_data["messages"][0])
            openai_data["messages"] = messages

        return openai_data

    async def _convert_huggingface_to_openai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将 HuggingFace 格式转换为 OpenAI 格式"""
        logger.debug("Converting HuggingFace format to OpenAI")

        openai_data = {
            "model": data.get("model", "huggingface/default"),
            "messages": [
                {
                    "role": "user",
                    "content": data.get("inputs", data.get("prompt", ""))
                }
            ],
            "temperature": data.get("parameters", {}).get("temperature", 1.0),
            "max_tokens": data.get("parameters", {}).get("max_new_tokens", 4096),
            "top_p": data.get("parameters", {}).get("top_p", 1.0),
        }

        return openai_data

    async def _convert_ollama_to_openai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将 Ollama 格式转换为 OpenAI 格式"""
        logger.debug("Converting Ollama format to OpenAI")

        openai_data = {
            "model": data.get("model", "llama2"),
            "messages": [],
            "temperature": data.get("options", {}).get("temperature", 1.0),
            "max_tokens": data.get("options", {}).get("num_predict"),
            "top_p": data.get("options", {}).get("top_p", 1.0),
        }

        # 处理消息或提示
        if "messages" in data:
            openai_data["messages"] = data["messages"]
        elif "prompt" in data:
            openai_data["messages"] = [
                {
                    "role": "user",
                    "content": data["prompt"]
                }
            ]

        return openai_data

    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return [fmt.value for fmt in self.format_converters.keys()]

    def is_format_supported(self, format_name: str) -> bool:
        """检查格式是否支持"""
        try:
            format_enum = LLMFormat(format_name.lower())
            return format_enum in self.format_converters
        except ValueError:
            return False
