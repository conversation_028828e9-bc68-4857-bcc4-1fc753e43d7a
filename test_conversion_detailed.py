#!/usr/bin/env python3
"""
详细的转换功能测试脚本
测试各种 LLM 格式的转换功能
"""

import asyncio
import json
import sys
from typing import Dict, Any

async def test_gemini_conversion():
    """测试 Gemini 格式转换"""
    print("Testing Gemini format conversion...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # Gemini 格式测试数据
        gemini_data = {
            "model": "gemini-pro",
            "contents": [
                {
                    "role": "user",
                    "parts": [
                        {"text": "Hello, how are you today?"}
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 1000,
                "topP": 0.9,
                "topK": 40
            }
        }
        
        result = await converter.convert_to_openai_format(gemini_data, LLMFormat.GEMINI)
        
        if result.success:
            print("✓ Gemini conversion successful")
            converted = result.converted_data
            print(f"✓ Model: {converted.get('model')}")
            print(f"✓ Messages count: {len(converted.get('messages', []))}")
            print(f"✓ Temperature: {converted.get('temperature')}")
            print(f"✓ Max tokens: {converted.get('max_tokens')}")
            
            # 验证消息格式
            messages = converted.get('messages', [])
            if messages and messages[0].get('role') == 'user':
                print("✓ Message format correct")
            else:
                print("✗ Message format incorrect")
                return False
        else:
            print(f"✗ Gemini conversion failed: {result.error_message}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Gemini conversion test failed: {e}")
        return False

async def test_claude_conversion():
    """测试 Claude 格式转换"""
    print("\nTesting Claude format conversion...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # Claude 格式测试数据
        claude_data = {
            "model": "claude-3-sonnet-20240229",
            "messages": [
                {
                    "role": "user",
                    "content": "What is the capital of France?"
                }
            ],
            "temperature": 0.5,
            "max_tokens": 500,
            "top_p": 0.8
        }
        
        result = await converter.convert_to_openai_format(claude_data, LLMFormat.CLAUDE)
        
        if result.success:
            print("✓ Claude conversion successful")
            converted = result.converted_data
            print(f"✓ Model: {converted.get('model')}")
            print(f"✓ Messages: {converted.get('messages')}")
            print(f"✓ Temperature: {converted.get('temperature')}")
        else:
            print(f"✗ Claude conversion failed: {result.error_message}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Claude conversion test failed: {e}")
        return False

async def test_cohere_conversion():
    """测试 Cohere 格式转换"""
    print("\nTesting Cohere format conversion...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # Cohere 格式测试数据
        cohere_data = {
            "model": "command",
            "message": "Tell me a joke",
            "temperature": 0.8,
            "max_tokens": 200,
            "chat_history": [
                {
                    "user_name": "User",
                    "message": "Hello"
                },
                {
                    "user_name": "Chatbot",
                    "message": "Hi there!"
                }
            ]
        }
        
        result = await converter.convert_to_openai_format(cohere_data, LLMFormat.COHERE)
        
        if result.success:
            print("✓ Cohere conversion successful")
            converted = result.converted_data
            print(f"✓ Model: {converted.get('model')}")
            print(f"✓ Messages count: {len(converted.get('messages', []))}")
            
            # 验证聊天历史转换
            messages = converted.get('messages', [])
            if len(messages) >= 3:  # 历史 + 当前消息
                print("✓ Chat history converted correctly")
            else:
                print("✗ Chat history conversion failed")
                return False
        else:
            print(f"✗ Cohere conversion failed: {result.error_message}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Cohere conversion test failed: {e}")
        return False

async def test_ollama_conversion():
    """测试 Ollama 格式转换"""
    print("\nTesting Ollama format conversion...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # Ollama 格式测试数据（使用 prompt）
        ollama_data_prompt = {
            "model": "llama2",
            "prompt": "Explain quantum computing in simple terms",
            "options": {
                "temperature": 0.6,
                "num_predict": 300,
                "top_p": 0.9
            }
        }
        
        result = await converter.convert_to_openai_format(ollama_data_prompt, LLMFormat.OLLAMA)
        
        if result.success:
            print("✓ Ollama (prompt) conversion successful")
            converted = result.converted_data
            print(f"✓ Model: {converted.get('model')}")
            print(f"✓ Messages: {converted.get('messages')}")
        else:
            print(f"✗ Ollama conversion failed: {result.error_message}")
            return False
        
        # Ollama 格式测试数据（使用 messages）
        ollama_data_messages = {
            "model": "llama2",
            "messages": [
                {
                    "role": "user",
                    "content": "What is machine learning?"
                }
            ],
            "options": {
                "temperature": 0.7
            }
        }
        
        result = await converter.convert_to_openai_format(ollama_data_messages, LLMFormat.OLLAMA)
        
        if result.success:
            print("✓ Ollama (messages) conversion successful")
        else:
            print(f"✗ Ollama messages conversion failed: {result.error_message}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Ollama conversion test failed: {e}")
        return False

async def test_openai_passthrough():
    """测试 OpenAI 格式直通"""
    print("\nTesting OpenAI format passthrough...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # OpenAI 格式测试数据
        openai_data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, world!"
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        result = await converter.convert_to_openai_format(openai_data, LLMFormat.OPENAI)
        
        if result.success:
            print("✓ OpenAI passthrough successful")
            # 验证数据未被修改
            if result.converted_data == openai_data:
                print("✓ Data unchanged (correct passthrough)")
            else:
                print("✗ Data was modified (incorrect passthrough)")
                return False
        else:
            print(f"✗ OpenAI passthrough failed: {result.error_message}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ OpenAI passthrough test failed: {e}")
        return False

async def test_error_handling():
    """测试错误处理"""
    print("\nTesting error handling...")
    try:
        from service.litellm_converter import LiteLLMConverter
        from models.conversion_models import LLMFormat
        
        converter = LiteLLMConverter()
        
        # 测试不支持的格式
        result = await converter.convert_to_openai_format({}, LLMFormat.UNKNOWN)
        
        if not result.success:
            print("✓ Unsupported format error handled correctly")
        else:
            print("✗ Should have failed for unknown format")
            return False
        
        # 测试空数据
        result = await converter.convert_to_openai_format({}, LLMFormat.GEMINI)
        
        if result.success:
            print("✓ Empty data handled gracefully")
        else:
            print("✓ Empty data error handled correctly")
        
        return True
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 70)
    print("LLM Proxy Server - Detailed Conversion Test")
    print("=" * 70)
    
    tests = [
        ("Gemini Conversion", test_gemini_conversion),
        ("Claude Conversion", test_claude_conversion),
        ("Cohere Conversion", test_cohere_conversion),
        ("Ollama Conversion", test_ollama_conversion),
        ("OpenAI Passthrough", test_openai_passthrough),
        ("Error Handling", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All conversion tests passed! LiteLLM Converter is fully functional.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
