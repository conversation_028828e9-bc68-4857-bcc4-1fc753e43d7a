#!/usr/bin/env python3
"""
测试目标URL配置
验证代理转发是否正确配置到 http://127.0.0.1:8001/v1
"""

import sys

def test_target_url_configuration():
    """测试目标URL配置"""
    print("Testing target URL configuration...")
    try:
        from config.settings import settings
        
        target_url = settings.TARGET_BASE_URL
        print(f"✓ Target URL configured: {target_url}")
        
        # 验证URL格式
        expected_url = "http://127.0.0.1:8001/v1"
        if target_url == expected_url:
            print(f"✓ Target URL matches expected: {expected_url}")
            return True
        else:
            print(f"✗ Target URL mismatch:")
            print(f"  Expected: {expected_url}")
            print(f"  Actual:   {target_url}")
            return False
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_proxy_forwarder_configuration():
    """测试代理转发器配置"""
    print("\nTesting proxy forwarder configuration...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder()
        target_url = forwarder.target_base_url
        
        print(f"✓ Proxy forwarder target URL: {target_url}")
        
        # 验证转发器使用正确的URL
        expected_url = "http://127.0.0.1:8001/v1"
        if target_url == expected_url:
            print(f"✓ Proxy forwarder correctly configured")
            return True
        else:
            print(f"✗ Proxy forwarder URL mismatch:")
            print(f"  Expected: {expected_url}")
            print(f"  Actual:   {target_url}")
            return False
        
    except Exception as e:
        print(f"✗ Proxy forwarder test failed: {e}")
        return False

def test_request_path_handling():
    """测试请求路径处理"""
    print("\nTesting request path handling...")
    try:
        from service.proxy_forwarder import ProxyForwarder
        
        forwarder = ProxyForwarder()
        base_url = forwarder.target_base_url
        
        # 测试不同的请求路径
        test_paths = [
            "/chat/completions",
            "/embeddings", 
            "/models",
            "/health"
        ]
        
        print("Request path examples:")
        for path in test_paths:
            full_url = f"{base_url}{path}"
            print(f"  {path} -> {full_url}")
        
        # 验证路径拼接是否正确
        expected_chat_url = "http://127.0.0.1:8001/v1/chat/completions"
        actual_chat_url = f"{base_url}/chat/completions"
        
        if actual_chat_url == expected_chat_url:
            print(f"✓ Path handling correct")
            return True
        else:
            print(f"✗ Path handling incorrect:")
            print(f"  Expected: {expected_chat_url}")
            print(f"  Actual:   {actual_chat_url}")
            return False
        
    except Exception as e:
        print(f"✗ Request path handling test failed: {e}")
        return False

def test_environment_variables():
    """测试环境变量"""
    print("\nTesting environment variables...")
    try:
        import os
        from config.settings import settings
        
        # 检查关键环境变量
        env_vars = {
            "TARGET_BASE_URL": os.getenv("TARGET_BASE_URL"),
            "PROXY_PORT": os.getenv("PROXY_PORT"),
            "TARGET_HOST": os.getenv("TARGET_HOST"),
            "TARGET_PORT": os.getenv("TARGET_PORT")
        }
        
        print("Environment variables:")
        for key, value in env_vars.items():
            if value:
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: (not set, using default)")
        
        # 验证最终配置
        print(f"\nFinal configuration:")
        print(f"  Proxy server: {settings.PROXY_HOST}:{settings.PROXY_PORT}")
        print(f"  Target server: {settings.TARGET_BASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"✗ Environment variables test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("LLM Proxy Server - Target URL Configuration Test")
    print("=" * 60)
    
    tests = [
        ("Target URL Configuration", test_target_url_configuration),
        ("Proxy Forwarder Configuration", test_proxy_forwarder_configuration),
        ("Request Path Handling", test_request_path_handling),
        ("Environment Variables", test_environment_variables),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All configuration tests passed!")
        print(f"✅ Proxy server will forward requests to: http://127.0.0.1:8001/v1")
        return 0
    else:
        print("❌ Some configuration tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
