@echo off
chcp 65001 >nul
echo ============================================================
echo LLM 代理服务器简化版服务管理工具
echo ============================================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 管理员权限检查通过
) else (
    echo ✗ 错误: 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 切换到脚本所在目录
cd /d "%~dp0"

:menu
echo.
echo ============================================
echo 请选择操作:
echo 1. 删除旧服务并安装新服务
echo 2. 启动服务
echo 3. 停止服务
echo 4. 重启服务
echo 5. 查看服务状态
echo 6. 测试 API 接口
echo 7. 查看服务日志
echo 8. 卸载服务
echo 0. 退出
echo ============================================
set /p choice=请输入选择 (0-8): 

if "%choice%"=="1" goto install_service
if "%choice%"=="2" goto start_service
if "%choice%"=="3" goto stop_service
if "%choice%"=="4" goto restart_service
if "%choice%"=="5" goto status_service
if "%choice%"=="6" goto test_api
if "%choice%"=="7" goto view_logs
if "%choice%"=="8" goto uninstall_service
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:install_service
echo.
echo 正在删除旧服务...
py service_wrapper.py remove 2>nul
py simple_service.py remove 2>nul
sc delete LLMProxyServer 2>nul
echo.
echo 正在安装新服务...
py simple_service.py install
if %errorLevel% == 0 (
    echo ✓ 服务安装成功
    echo 正在设置为自动启动...
    sc config LLMProxyServer start= auto
    if %errorLevel__ == 0 (
        echo ✓ 已设置为自动启动
    ) else (
        echo ⚠ 设置自动启动失败
    )
) else (
    echo ✗ 服务安装失败
)
echo.
pause
goto menu

:start_service
echo.
echo 正在启动服务...
py simple_service.py start
if %errorLevel% == 0 (
    echo ✓ 服务启动成功
    timeout /t 3 >nul
    goto status_service
) else (
    echo ✗ 服务启动失败
)
echo.
pause
goto menu

:stop_service
echo.
echo 正在停止服务...
py simple_service.py stop
if %errorLevel% == 0 (
    echo ✓ 服务停止成功
) else (
    echo ✗ 服务停止失败
)
echo.
pause
goto menu

:restart_service
echo.
echo 正在重启服务...
py simple_service.py stop
timeout /t 3 >nul
py simple_service.py start
if %errorLevel% == 0 (
    echo ✓ 服务重启成功
    timeout /t 3 >nul
    goto status_service
) else (
    echo ✗ 服务重启失败
)
echo.
pause
goto menu

:status_service
echo.
echo 服务状态:
sc query LLMProxyServer
echo.
echo 端口监听状态:
netstat -an | findstr :8002
echo.
pause
goto menu

:test_api
echo.
echo 正在测试 API 接口...
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:8002/health -Method GET -TimeoutSec 10; Write-Host '✓ API 测试成功'; Write-Host $response.Content } catch { Write-Host '✗ API 测试失败:' $_.Exception.Message }"
echo.
pause
goto menu

:view_logs
echo.
echo 查看服务日志 (最近50行):
if exist "logs\service.log" (
    powershell -Command "Get-Content 'logs\service.log' -Tail 50"
) else (
    echo 日志文件不存在
)
echo.
pause
goto menu

:uninstall_service
echo.
echo 正在卸载服务...
py simple_service.py stop 2>nul
py simple_service.py remove
if %errorLevel% == 0 (
    echo ✓ 服务卸载成功
) else (
    echo ✗ 服务卸载失败
)
echo.
pause
goto menu

:exit
echo 退出程序
exit /b 0
