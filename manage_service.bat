@echo off
chcp 65001 >nul
echo ============================================================
echo LLM 代理服务器 Windows 服务管理工具
echo ============================================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 管理员权限检查通过
) else (
    echo ✗ 错误: 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 切换到脚本所在目录
cd /d "%~dp0"

:menu
echo.
echo ============================================
echo 请选择操作:
echo 1. 启动服务
echo 2. 停止服务
echo 3. 重启服务
echo 4. 查看服务状态
echo 5. 测试 API 接口
echo 6. 查看服务日志
echo 7. 卸载服务
echo 0. 退出
echo ============================================
set /p choice=请输入选择 (0-7): 

if "%choice%"=="1" goto start_service
if "%choice%"=="2" goto stop_service
if "%choice%"=="3" goto restart_service
if "%choice%"=="4" goto status_service
if "%choice%"=="5" goto test_api
if "%choice%"=="6" goto view_logs
if "%choice%"=="7" goto uninstall_service
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:start_service
echo.
echo 正在启动服务...
sc start LLMProxyServer
timeout /t 3 >nul
goto status_service

:stop_service
echo.
echo 正在停止服务...
sc stop LLMProxyServer
timeout /t 3 >nul
goto status_service

:restart_service
echo.
echo 正在重启服务...
sc stop LLMProxyServer
timeout /t 3 >nul
sc start LLMProxyServer
timeout /t 3 >nul
goto status_service

:status_service
echo.
echo 服务状态:
sc query LLMProxyServer
echo.
pause
goto menu

:test_api
echo.
echo 正在测试 API 接口...
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:8002/health -Method GET -TimeoutSec 10; Write-Host '✓ API 测试成功'; Write-Host $response.Content } catch { Write-Host '✗ API 测试失败:' $_.Exception.Message }"
echo.
pause
goto menu

:view_logs
echo.
echo 查看服务日志 (最近50行):
if exist "logs\service.log" (
    powershell -Command "Get-Content 'logs\service.log' -Tail 50"
) else (
    echo 日志文件不存在
)
echo.
pause
goto menu

:uninstall_service
echo.
echo 正在卸载服务...
py service_wrapper.py remove
echo.
pause
goto menu

:exit
echo 退出程序
exit /b 0
